{"cells": [{"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-07-27T02:03:04.667361Z", "start_time": "2025-07-27T02:03:04.654319Z"}}, "source": ["import os\n", "import json\n", "import gzip\n", "import numpy as np\n", "import pandas as pd \n", "import matplotlib.pyplot as plt\n", "import random\n", "from collections import defaultdict\n", "from tqdm import tqdm\n", "random.seed(2024)"], "outputs": [], "execution_count": 14}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-07-27T02:02:49.163822Z", "start_time": "2025-07-27T02:02:49.133862Z"}}, "source": ["# Extract Data (only save the shared data between two jsons)\n", "id_to_title_map = {}\n", "asin_set = []\n", "with gzip.open('./raw_data/meta_Video_Games.json.gz', 'r') as file:\n", "    for line in file:\n", "        data = json.loads(line)\n", "        if \"asin\" in data:\n", "            asin_set.append(data['asin'])\n", "asin_set = set(asin_set)\n", "\n", "inter_dict = {}\n", "with gzip.open('./raw_data/Video_Games_5.json.gz', 'r') as file:\n", "    for line in file:\n", "        review = json.loads(line)\n", "        if review['overall'] < 0 or review['asin'] not in asin_set or \"reviewerName\" not in review:\n", "            continue\n", "        u_id, i_id, time = review['reviewerID'], review['asin'], review['unixReviewTime']\n", "        if u_id not in inter_dict:\n", "            inter_dict[u_id] = {}\n", "        inter_dict[u_id][i_id] = time\n", "print('raw user num:',len(inter_dict))"], "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: './raw_data/meta_Video_Games.json.gz'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[13], line 4\u001b[0m\n\u001b[0;32m      2\u001b[0m id_to_title_map \u001b[38;5;241m=\u001b[39m {}\n\u001b[0;32m      3\u001b[0m asin_set \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m----> 4\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[43mgzip\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mopen\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m./raw_data/meta_Video_Games.json.gz\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mr\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mas\u001b[39;00m file:\n\u001b[0;32m      5\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m line \u001b[38;5;129;01min\u001b[39;00m file:\n\u001b[0;32m      6\u001b[0m         data \u001b[38;5;241m=\u001b[39m json\u001b[38;5;241m.\u001b[39mloads(line)\n", "File \u001b[1;32m~\\anaconda3\\envs\\FELLRec-main\\lib\\gzip.py:58\u001b[0m, in \u001b[0;36mopen\u001b[1;34m(filename, mode, compresslevel, encoding, errors, newline)\u001b[0m\n\u001b[0;32m     56\u001b[0m gz_mode \u001b[38;5;241m=\u001b[39m mode\u001b[38;5;241m.\u001b[39mreplace(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mt\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     57\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(filename, (\u001b[38;5;28mstr\u001b[39m, \u001b[38;5;28mbytes\u001b[39m, os\u001b[38;5;241m.\u001b[39mPathLike)):\n\u001b[1;32m---> 58\u001b[0m     binary_file \u001b[38;5;241m=\u001b[39m \u001b[43mGzipFile\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilename\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mgz_mode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcompresslevel\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     59\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(filename, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mread\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(filename, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mwrite\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m     60\u001b[0m     binary_file \u001b[38;5;241m=\u001b[39m GzipFile(\u001b[38;5;28;01mNone\u001b[39;00m, gz_mode, compresslevel, filename)\n", "File \u001b[1;32m~\\anaconda3\\envs\\FELLRec-main\\lib\\gzip.py:173\u001b[0m, in \u001b[0;36mGzipFile.__init__\u001b[1;34m(self, filename, mode, compresslevel, fileobj, mtime)\u001b[0m\n\u001b[0;32m    171\u001b[0m     mode \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m    172\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m fileobj \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m--> 173\u001b[0m     fileobj \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmyfileobj \u001b[38;5;241m=\u001b[39m \u001b[43mbuiltins\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilename\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mrb\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m    174\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m filename \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m    175\u001b[0m     filename \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(fileobj, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mname\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: './raw_data/meta_Video_Games.json.gz'"]}], "execution_count": 13}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-07-27T02:02:49.165741Z", "start_time": "2025-07-27T02:02:19.697656Z"}}, "source": ["def select_kcore(interaction_dict, user_K, item_K):\n", "    flag = 0\n", "    while flag == 0:\n", "        item_cnt_dict = {}\n", "        item_drop_dict = {}\n", "\n", "        # create item_drop_dict, item_cnt_dict\n", "        for user_id in interaction_dict:\n", "            for item_id, time in interaction_dict[user_id].items():\n", "                if item_id not in item_cnt_dict:\n", "                    item_cnt_dict[item_id] = 0\n", "                    item_drop_dict[item_id] = 0\n", "                item_cnt_dict[item_id] += 1\n", "\n", "        assert len(item_drop_dict)==len(item_cnt_dict)\n", "\n", "        # delete items < K\n", "        del_iid_list = []\n", "        for i_id in item_cnt_dict:\n", "            if item_cnt_dict[i_id] < item_K:\n", "                del_iid_list.append(i_id)\n", "\n", "        for i_id in del_iid_list:\n", "            item_drop_dict[i_id] = 1\n", "        for u_id in interaction_dict:\n", "            del_id_list = []\n", "            for i_id, _ in interaction_dict[u_id].items():\n", "                if item_drop_dict[i_id]:\n", "                    del_id_list.append(i_id)\n", "            for del_id in del_id_list:\n", "                del interaction_dict[u_id][del_id]\n", "\n", "        item_drop_num = 0\n", "        for i_id in item_drop_dict:\n", "            item_drop_num += item_drop_dict[i_id]\n", "        item_num = len(item_drop_dict) - item_drop_num\n", "        #print(f'item num after item-{K}core:',item_num)\n", "\n", "        new_item_cnt = {}\n", "        min_cnt = 999\n", "        for u_id in interaction_dict:\n", "            min_cnt = min(min_cnt, len(interaction_dict[u_id]))\n", "            for i_id, _ in interaction_dict[u_id].items():\n", "                if i_id not in new_item_cnt:\n", "                    new_item_cnt[i_id] = 0\n", "                new_item_cnt[i_id] += 1\n", "\n", "        min_cnt_item = 999\n", "        for i_id in new_item_cnt:\n", "            min_cnt_item = min(min_cnt_item, new_item_cnt[i_id])\n", "        #print('min item num:',min_cnt_item)\n", "        if min_cnt >= user_K and min_cnt_item >= item_K:\n", "            return interaction_dict, len(interaction_dict), item_num\n", "        \n", "        # delete users interactions < K\n", "        del_uid_list = []\n", "        for u_id in interaction_dict:\n", "            if len(interaction_dict[u_id]) < user_K:\n", "                del_uid_list.append(u_id)\n", "        for u_id in del_uid_list:\n", "            del interaction_dict[u_id]\n", "        \n", "        # count min user-interaction and item appearance\n", "        new_item_cnt = {}\n", "        min_cnt = 999\n", "        for u_id in interaction_dict:\n", "            min_cnt = min(min_cnt, len(interaction_dict[u_id]))\n", "            for i_id, _ in interaction_dict[u_id].items():\n", "                if i_id not in new_item_cnt:\n", "                    new_item_cnt[i_id] = 0\n", "                new_item_cnt[i_id] += 1\n", "\n", "        min_cnt_item = 999\n", "        for i_id in new_item_cnt:\n", "            min_cnt_item = min(min_cnt_item, new_item_cnt[i_id])     \n", "        if min_cnt >= user_K and min_cnt_item >= item_K:\n", "            return interaction_dict, len(interaction_dict), item_num\n", "\n", "interaction_dict, user_num, item_num = select_kcore(inter_dict, 5, 5)\n", "print('uesr num after k core',user_num)\n", "print('item num after k core',item_num)\n", "inter_num = 0\n", "for u_id in interaction_dict:\n", "    inter_num += len(interaction_dict[u_id])\n", "density = inter_num / (user_num * item_num)\n", "print(f'interaction number: {inter_num}')\n", "print(f'density: {density}')\n", "\n", "for u_id in interaction_dict:\n", "    interaction_dict[u_id] = dict(sorted(interaction_dict[u_id].items(),key=lambda item:item[1]))\n", "    \n", "for u_id in interaction_dict:\n", "    print(interaction_dict[u_id])\n", "    break"], "outputs": [{"ename": "NameError", "evalue": "name 'inter_dict' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 79\u001b[0m\n\u001b[0;32m     76\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m min_cnt \u001b[38;5;241m>\u001b[39m\u001b[38;5;241m=\u001b[39m user_K \u001b[38;5;129;01mand\u001b[39;00m min_cnt_item \u001b[38;5;241m>\u001b[39m\u001b[38;5;241m=\u001b[39m item_K:\n\u001b[0;32m     77\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m interaction_dict, \u001b[38;5;28mlen\u001b[39m(interaction_dict), item_num\n\u001b[1;32m---> 79\u001b[0m interaction_dict, user_num, item_num \u001b[38;5;241m=\u001b[39m select_kcore(\u001b[43minter_dict\u001b[49m, \u001b[38;5;241m5\u001b[39m, \u001b[38;5;241m5\u001b[39m)\n\u001b[0;32m     80\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124muesr num after k core\u001b[39m\u001b[38;5;124m'\u001b[39m,user_num)\n\u001b[0;32m     81\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mitem num after k core\u001b[39m\u001b[38;5;124m'\u001b[39m,item_num)\n", "\u001b[1;31mNameError\u001b[0m: name 'inter_dict' is not defined"]}], "execution_count": 2}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-07-27T02:02:49.169736500Z", "start_time": "2025-07-27T02:02:30.044501Z"}}, "source": ["# mapping user and item id\n", "item_list = []\n", "user_mapping = {key: i for i, key in enumerate(interaction_dict.keys())}\n", "for u_id in interaction_dict:\n", "    for i_id in interaction_dict[u_id]:\n", "        item_list.append(i_id)\n", "item_set = list(set(item_list))\n", "random.shuffle(item_set)\n", "item_mapping = {value: i for i, value in enumerate(item_set)}"], "outputs": [{"ename": "NameError", "evalue": "name 'interaction_dict' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[3], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# mapping user and item id\u001b[39;00m\n\u001b[0;32m      2\u001b[0m item_list \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m----> 3\u001b[0m user_mapping \u001b[38;5;241m=\u001b[39m {key: i \u001b[38;5;28;01mfor\u001b[39;00m i, key \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(\u001b[43minteraction_dict\u001b[49m\u001b[38;5;241m.\u001b[39mkeys())}\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m u_id \u001b[38;5;129;01min\u001b[39;00m interaction_dict:\n\u001b[0;32m      5\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m i_id \u001b[38;5;129;01min\u001b[39;00m interaction_dict[u_id]:\n", "\u001b[1;31mNameError\u001b[0m: name 'interaction_dict' is not defined"]}], "execution_count": 3}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-07-27T02:02:49.169736500Z", "start_time": "2025-07-27T02:02:33.039870Z"}}, "source": ["# split the dataset following global split\n", "time_list = []\n", "for u_id in interaction_dict:\n", "    for i_id in interaction_dict[u_id]:\n", "        time_list.append(interaction_dict[u_id][i_id])\n", "time_list = sorted(time_list)\n", "plt.hist(time_list,density=True)\n", "\n", "test_ratio = 0.1\n", "test_num = int(0.1*len(time_list))\n", "time_list = sorted(time_list)\n", "print(f'train time interval:{time_list[0]}-{time_list[int(-2*test_num)]}')\n", "print(f'valid time interval:{time_list[int(-2*test_num)]}-{time_list[-test_num]}')\n", "print(f'test time interval:{time_list[-test_num]}-{time_list[-1]}')\n", "\n", "train_interval = [time_list[0], time_list[int(-2*test_num)]]\n", "valid_interval = [time_list[int(-2*test_num)], time_list[-test_num]]\n", "test_interval = [time_list[-test_num],time_list[-1]]\n", "\n", "def time2set(t):\n", "    if t<train_interval[1]:\n", "        return 0\n", "    elif t<valid_interval[1]:\n", "        return 1\n", "    else:\n", "        return 2\n", "\n", "train_dict, valid_dict, test_dict = {}, {}, {}\n", "for u_id in interaction_dict:\n", "    for i_id, time in interaction_dict[u_id].items():\n", "        split_set = time2set(time)\n", "        if split_set == 0:\n", "            if user_mapping[u_id] not in train_dict:\n", "                train_dict[user_mapping[u_id]] = []\n", "            train_dict[user_mapping[u_id]].append(item_mapping[i_id])\n", "        elif split_set == 1:\n", "            if user_mapping[u_id] not in valid_dict:\n", "                valid_dict[user_mapping[u_id]] = []\n", "            valid_dict[user_mapping[u_id]].append(item_mapping[i_id])\n", "        else:\n", "            if user_mapping[u_id] not in test_dict:\n", "                test_dict[user_mapping[u_id]] = []\n", "            test_dict[user_mapping[u_id]].append(item_mapping[i_id])"], "outputs": [{"ename": "NameError", "evalue": "name 'interaction_dict' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# split the dataset following global split\u001b[39;00m\n\u001b[0;32m      2\u001b[0m time_list \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m----> 3\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m u_id \u001b[38;5;129;01min\u001b[39;00m \u001b[43minteraction_dict\u001b[49m:\n\u001b[0;32m      4\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m i_id \u001b[38;5;129;01min\u001b[39;00m interaction_dict[u_id]:\n\u001b[0;32m      5\u001b[0m         time_list\u001b[38;5;241m.\u001b[39mappend(interaction_dict[u_id][i_id])\n", "\u001b[1;31mNameError\u001b[0m: name 'interaction_dict' is not defined"]}], "execution_count": 4}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-07-27T02:02:49.170736800Z", "start_time": "2025-07-27T02:02:36.597568Z"}}, "source": ["def remove_cold_users_items_iteratively(train, valid, test, user_map, item_map):\n", "    \"\"\"\n", "    Iteratively remove cold users and items and remap user and item IDs.\n", "\n", "    Args:\n", "    - train (dict): Training data, where key is user ID and value is list of item IDs.\n", "    - valid (dict): Validation data, similar to train.\n", "    - test (dict): Test data, similar to train.\n", "    - min_interactions (int): Minimum number of interactions for a user or item to be kept.\n", "\n", "    Returns:\n", "    - dict: New train, valid, test datasets with remapped user and item IDs.\n", "    \"\"\"\n", "    from collections import defaultdict\n", "\n", "    def get_cold_user_item(train, valid, test):\n", "        drop_user, drop_item = [], []\n", "\n", "        for u_id in valid:\n", "            if u_id not in train:\n", "                drop_user.append(u_id)\n", "        for u_id in test:\n", "            if u_id not in train:\n", "                drop_user.append(u_id)\n", "\n", "        item_set = set()\n", "        for u_id in train:\n", "            if len(train[u_id]) < 2:\n", "                drop_user.append(u_id)\n", "            for i_id in train[u_id]:\n", "                item_set.add(i_id)\n", "        for u_id in valid:\n", "            for i_id in valid[u_id]:\n", "                if i_id not in item_set:\n", "                    drop_item.append(i_id)\n", "        for u_id in test:\n", "            for i_id in test[u_id]:\n", "                if i_id not in item_set:\n", "                    drop_item.append(i_id)\n", "\n", "        drop_user = list(set(drop_user))\n", "        drop_item = list(set(drop_item))\n", "        return [], []\n", "\n", "    def get_all_interaction(train, valid, test):\n", "        user_set, item_set = set(), set()\n", "        for u_id in train:\n", "            user_set.add(u_id)\n", "            for i_id in train[u_id]:\n", "                item_set.add(i_id)\n", "        for u_id in valid:\n", "            user_set.add(u_id)\n", "            for i_id in valid[u_id]:\n", "                item_set.add(i_id)\n", "        for u_id in test:\n", "            user_set.add(u_id)\n", "            for i_id in test[u_id]:\n", "                item_set.add(i_id)\n", "        return list(user_set), list(item_set)\n", "\n", "    def remap_ids(all_list, drop_list):\n", "        drop_set = set(drop_list)\n", "\n", "        # Remove elements present in drop_list\n", "        remaining_elements = [element for element in all_list if element not in drop_set]\n", "\n", "        # Remap remaining elements\n", "        unique_elements = sorted(set(remaining_elements))  # Get unique elements and sort\n", "        remap_dict = {element: new_id for new_id, element in enumerate(unique_elements)}\n", "\n", "        return remap_dict\n", "\n", "    def remap_dataset(dataset, user_id_map, item_id_map):\n", "        new_dataset = {}\n", "        for user, items in dataset.items():\n", "            if user in user_id_map:\n", "                new_items = [item_id_map[item] for item in items if item in item_id_map]\n", "                if new_items:\n", "                    new_dataset[user_id_map[user]] = new_items\n", "        return new_dataset\n", "\n", "    def map_update(id_map, semantic_map):\n", "        # id_map: key:old id; value:new id\n", "        # semantic_map: key:asin; value: old id\n", "        # what I want: key:asin, value: new id\n", "        new_map = {}\n", "        for asin in semantic_map:\n", "            if semantic_map[asin] in id_map:\n", "                new_map[asin] = id_map[semantic_map[asin]]\n", "        return new_map\n", "    \n", "    # Iteratively remove cold users and items\n", "    cnt = 0\n", "    while True:\n", "        cold_user, cold_item = get_cold_user_item(train, valid, test)\n", "        user_set, item_set = get_all_interaction(train, valid, test)\n", "        if cnt == 0:\n", "            new_user_map = user_map\n", "            new_item_map = item_map\n", "\n", "        if not cold_user and not cold_item:\n", "            print(cnt)\n", "            break  # No more cold users or items\n", "\n", "        user_id_map = remap_ids(user_set, cold_user)\n", "        item_id_map = remap_ids(item_set, cold_item)\n", "        \n", "        new_user_map = map_update(user_id_map, new_user_map)\n", "        new_item_map = map_update(item_id_map, new_item_map)\n", "\n", "        train = remap_dataset(train, user_id_map, item_id_map)\n", "        valid = remap_dataset(valid, user_id_map, item_id_map)\n", "        test = remap_dataset(test, user_id_map, item_id_map)\n", "\n", "        cnt += 1\n", "    return train, valid, test, new_user_map, new_item_map\n", "\n", "new_train, new_valid, new_test, new_user_mapping, new_item_mapping = remove_cold_users_items_iteratively(train_dict, valid_dict, test_dict, user_mapping, item_mapping)\n", "train_list, valid_list, test_list = [], [], []\n", "for u_id in new_train:\n", "    for i_id in new_train[u_id]:\n", "        train_list.append([u_id, i_id])\n", "for u_id in new_valid:\n", "    for i_id in new_valid[u_id]:\n", "        valid_list.append([u_id, i_id])\n", "for u_id in new_test:\n", "    for i_id in new_test[u_id]:\n", "        test_list.append([u_id, i_id])"], "outputs": [{"ename": "NameError", "evalue": "name 'train_dict' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[5], line 118\u001b[0m\n\u001b[0;32m    115\u001b[0m         cnt \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[0;32m    116\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m train, valid, test, new_user_map, new_item_map\n\u001b[1;32m--> 118\u001b[0m new_train, new_valid, new_test, new_user_mapping, new_item_mapping \u001b[38;5;241m=\u001b[39m remove_cold_users_items_iteratively(\u001b[43mtrain_dict\u001b[49m, valid_dict, test_dict, user_mapping, item_mapping)\n\u001b[0;32m    119\u001b[0m train_list, valid_list, test_list \u001b[38;5;241m=\u001b[39m [], [], []\n\u001b[0;32m    120\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m u_id \u001b[38;5;129;01min\u001b[39;00m new_train:\n", "\u001b[1;31mNameError\u001b[0m: name 'train_dict' is not defined"]}], "execution_count": 5}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# save data\n", "save_path = './'\n", "\n", "np.save(save_path + 'train_list.npy', np.array(train_list))\n", "np.save(save_path + 'valid_list.npy', np.array(valid_list))\n", "np.save(save_path + 'test_list.npy', np.array(test_list))\n", "\n", "np.save(save_path + 'training_dict.npy', new_train)\n", "np.save(save_path + 'validation_dict.npy', new_valid)\n", "np.save(save_path + 'testing_dict.npy', new_test)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["User number: 45817\n", "Item number: 16132\n", "Inter num: 452894\n", "density: 0.0005316793498231202\n"]}], "source": ["# Statistical Information\n", "train_dict, valid_dict, test_dict = new_train, new_valid, new_test\n", "print(f'User number: {len(train_dict)}')\n", "item_list = []\n", "inter_num = 0\n", "for u_id in train_dict:\n", "    item_list.extend(train_dict[u_id])\n", "    inter_num += len(train_dict[u_id])\n", "for u_id in valid_dict:\n", "    inter_num += len(valid_dict[u_id])\n", "for u_id in test_dict:\n", "    inter_num += len(test_dict[u_id])\n", "item_list = list(set(item_list))\n", "print(f'Item number: {len(item_list)}')\n", "print(f'Inter num: {inter_num}')\n", "density = inter_num / (user_num * item_num)\n", "print(f'density: {density}')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["empty history item: 1530\n", "110404\n", "29877\n", "11936\n"]}], "source": ["# update sequential interactions\n", "# update train\n", "train_interactions = []\n", "cnt = 0\n", "for u_id in train_dict:\n", "    for i in range(min(10, len(train_dict[u_id]) - 1), len(train_dict[u_id])):\n", "        st = max(i-10, 0)\n", "        history_item = [item for item in train_dict[u_id][st:i]]\n", "        if history_item == []:\n", "            cnt += 1\n", "            continue\n", "        train_interactions.append([u_id, history_item, train_dict[u_id][i]])\n", "print(f'empty history item: {cnt}')\n", "        \n", "# update valid\n", "valid_interactions = []\n", "for u_id in valid_dict:\n", "    for cnt, i in enumerate(valid_dict[u_id]):\n", "        if u_id not in train_dict:\n", "            continue\n", "        else:\n", "            if cnt == 0:\n", "                history_item = train_dict[u_id][-10:]\n", "            elif cnt < 10:\n", "                history_item = train_dict[u_id][-10+cnt:] + valid_dict[u_id][:cnt]\n", "            else:\n", "                history_item = valid_dict[u_id][cnt-10:cnt]\n", "        valid_interactions.append([u_id, history_item, valid_dict[u_id][cnt]])\n", "\n", "# update test\n", "test_interactions = []\n", "for u_id in test_dict:\n", "    if u_id in valid_dict and u_id in train_dict:\n", "        train_valid_item = train_dict[u_id] + valid_dict[u_id]\n", "    elif u_id in valid_dict:\n", "        train_valid_item = valid_dict[u_id]\n", "    elif u_id in train_dict:\n", "        train_valid_item = train_dict[u_id]\n", "    else:\n", "        continue\n", "    history_item = train_valid_item[-10:]\n", "    test_interactions.append([u_id, history_item, test_dict[u_id]])\n", "\n", "print(len(train_interactions))\n", "print(len(valid_interactions))\n", "print(len(test_interactions))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# get the item id to title map\n", "save_path = './'\n", "id_to_title_map = {}\n", "with gzip.open('./raw_data/meta_Video_Games.json.gz', 'r') as file:\n", "    for line in file:\n", "        data = json.loads(line)\n", "        if \"asin\" in data:\n", "            id_to_title_map[data['asin']] = data['title']\n", "integer_to_title_map = {}\n", "for asin, id in new_item_mapping.items():\n", "    integer_to_title_map[id] = id_to_title_map[asin]\n", "\n", "np.save(save_path + 'id_to_title_map.npy', integer_to_title_map)\n", "np.save(save_path + 'asin_to_id_map.npy', new_item_mapping)\n", "np.save(save_path + 'userasin_to_id_map.npy', new_user_mapping)\n", "# Get the user id to name map\n", "id_to_name_map = {}\n", "with gzip.open('./raw_data/Video_Games_5.json.gz', 'r') as file:\n", "    for line in file:\n", "        data = json.loads(line)\n", "        if \"reviewerID\" in data and \"reviewerName\" in data:\n", "            id_to_name_map[data['reviewerID']] = data['reviewerName']\n", "integer_to_name_map = {}\n", "for reviewid, uid in new_user_mapping.items():\n", "    integer_to_name_map[uid] = id_to_name_map[reviewid]\n", "\n", "np.save('./id_to_name_map.npy', integer_to_name_map)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def csv_to_json_test(interactions, output_path, sample=False):\n", "    json_list = []\n", "    for user, history_item, target_item in interactions:\n", "        history = \"The user has played the following video games before:\"\n", "        for i in range(len(history_item)):\n", "            if i == 0:\n", "                history += \"\\\"\" + integer_to_title_map[history_item[i]] + \"\\\"\"\n", "            else:\n", "                history += \", \\\"\" + integer_to_title_map[history_item[i]] + \"\\\"\"\n", "        for i in range(len(target_item)):\n", "            if i == 0:\n", "                target_movie = \"\\\"\" + integer_to_title_map[target_item[i]] + \"\\\"\"\n", "            else:\n", "                target_movie += \", \\\"\" + integer_to_title_map[target_item[i]] + \"\\\"\"\n", "        json_list.append({\n", "            \"instruction\": \"Given a list of video games the user has played before, please recommend a new video game that the user likes to the user.\",\n", "            \"input\": f\"{history}\\n \",\n", "            \"output\": target_movie,\n", "        })  \n", "    if sample == True:\n", "        json_list = random.sample(json_list, 1024)\n", "    \n", "    with open(output_path, 'w') as f:\n", "        json.dump(json_list, f, indent=4)\n", "\n", "def csv_to_json_test_user(interactions, output_path, sample=False):\n", "    json_list = []\n", "    for user, history_item, target_item in interactions:\n", "        history = \"The user has played the following video games before:\"\n", "        for i in range(len(history_item)):\n", "            if i == 0:\n", "                history += \"\\\"\" + integer_to_title_map[history_item[i]] + \"\\\"\"\n", "            else:\n", "                history += \", \\\"\" + integer_to_title_map[history_item[i]] + \"\\\"\"\n", "        for i in range(len(target_item)):\n", "            if i == 0:\n", "                target_movie = \"\\\"\" + integer_to_title_map[target_item[i]] + \"\\\"\"\n", "            else:\n", "                target_movie += \", \\\"\" + integer_to_title_map[target_item[i]] + \"\\\"\"\n", "        json_list.append({\n", "            \"instruction\": \"Given a list of video games the user has played before, please recommend a new video game that the user likes to the user.\",\n", "            \"input\": f\"{history}\\n \",\n", "            \"output\": target_movie,\n", "            \"user\": user,\n", "        })  \n", "    if sample == True:\n", "        json_list = random.sample(json_list, 1024)\n", "    \n", "    with open(output_path, 'w') as f:\n", "        json.dump(json_list, f, indent=4)\n", "\n", "def csv_to_json(interactions, output_path, output_user_path, sample_num, sample=False):\n", "    json_list = []\n", "    for user, history_item, target_item in interactions:\n", "        history = \"The user has played the following video games before:\"\n", "        for i in range(len(history_item)):\n", "            if i == 0:\n", "                history += \"\\\"\" + integer_to_title_map[history_item[i]] + \"\\\"\"\n", "            else:\n", "                history += \", \\\"\" + integer_to_title_map[history_item[i]] + \"\\\"\"\n", "        target_movie = integer_to_title_map[target_item]\n", "        target_movie_str = \"\\\"\" + target_movie + \"\\\"\"\n", "\n", "        json_list.append({\n", "            \"instruction\": \"Given a list of video games the user has played before, please recommend a new video game that the user likes to the user.\",\n", "            \"input\": f\"{history}\\n \",\n", "            \"output\": target_movie_str,\n", "            \"user\": user,\n", "        })  \n", "    if sample == True:\n", "        json_list = random.sample(json_list, sample_num)\n", "    \n", "    json_list_without_user = []\n", "    for item in json_list:\n", "        json_list_without_user.append({\n", "            \"instruction\": \"Given a list of video games the user has played before, please recommend a new video game that the user likes to the user.\",\n", "            \"input\": item[\"input\"],\n", "            \"output\": item[\"output\"],\n", "        })\n", "\n", "    with open(output_path, 'w') as f:\n", "        json.dump(json_list_without_user, f, indent=4)\n", "    with open(output_user_path, 'w') as f:\n", "        json.dump(json_list, f, indent=4)\n", "csv_to_json(train_interactions, './train.json','./train_user.json',  0)\n", "csv_to_json(train_interactions, './train_1024.json', './train_1024_user.json', 1024, sample=True)\n", "csv_to_json(train_interactions, './train_2048.json', './train_2048_user.json', 2048, sample=True)\n", "csv_to_json(train_interactions, './train_4096.json', './train_4096_user.json', 4096, sample=True)\n", "csv_to_json(valid_interactions, './valid.json', './valid_user.json', 0)\n", "csv_to_json(valid_interactions, './valid_5000.json', './valid_5000_user.json', 5000, sample=True)\n", "csv_to_json_test(test_interactions, './test.json')\n", "csv_to_json_test_user(test_interactions, './test_user.json')\n", "# csv_to_json(train_interactions, './train_1024_user.json', 1024, sample=True, with_user=True)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# get train, valid, test dict for TIGER\n", "with open('train_interactions.json', 'w') as file:\n", "    json.dump(train_interactions, file)\n", "    "]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["with open('test_interactions.json', 'w') as file:\n", "    json.dump(test_interactions, file)\n", "\n", "with open('valid_interactions.json', 'w') as file:\n", "    json.dump(valid_interactions, file)"]}], "metadata": {"kernelspec": {"display_name": "P5", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 2}
"""
FELLRec联邦学习推荐系统 - 推理脚本

==============================================================================
脚本功能说明：
==============================================================================
这个脚本是FELLRec系统的推理模块，用于加载训练好的联邦学习推荐模型，
对测试数据进行推理，生成个性化推荐结果。

主要功能：
1. 加载每个客户端训练好的最佳模型
2. 对测试数据进行批量推理
3. 生成推荐结果并保存为JSON格式
4. 支持多种硬件设备（GPU/CPU/MPS）

推理流程：
客户端数据加载 → 模型加载 → 批量推理 → 结果保存 → 下一个客户端

注意事项：
- 每个客户端使用独立的模型进行推理
- 推理结果会合并保存到统一的JSON文件中
- 支持批量处理以提高推理效率
==============================================================================
"""

# ==================== 系统和基础库导入 ====================
import sys  # 系统相关功能，用于平台检测

import fire  # 命令行接口库，将函数转换为命令行工具
import gradio as gr  # Web界面库（本脚本中未使用，但保留以备扩展）
import torch  # PyTorch深度学习框架
torch.set_num_threads(1)  # 限制PyTorch使用的CPU线程数，避免资源竞争

import transformers  # HuggingFace Transformers库，用于加载预训练模型
import json  # JSON数据处理库
import os  # 操作系统接口库

# ==================== 性能优化环境变量设置 ====================
# 这些设置是为了避免多线程冲突和提高推理性能
os.environ['OPENBLAS_NUM_THREADS'] = '1'  # 限制OpenBLAS库的线程数
os.environ['OMP_NUM_THREADS'] = '1'       # 限制OpenMP的线程数

# ==================== GPU设备配置 ====================
os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # 指定使用第0号GPU设备

# ==================== 模型相关库导入 ====================
from peft import PeftModel  # PEFT库，用于加载LoRA微调后的模型
from transformers import GenerationConfig, LlamaTokenizer  # 生成配置和分词器
from transformers import LlamaForCausalLM  # LLaMA因果语言模型
import pickle  # Python对象序列化库，用于加载客户端数据

# ==================== 设备自动检测和配置 ====================
# 这部分代码自动检测可用的计算设备，优先使用GPU加速推理
if torch.cuda.is_available():
    device = "cuda"  # 如果有NVIDIA GPU，使用CUDA
    print("🚀 检测到CUDA设备，将使用GPU加速推理")
else:
    device = "cpu"   # 否则使用CPU
    print("💻 未检测到CUDA设备，将使用CPU进行推理")

# 检测Apple Silicon的MPS支持（适用于M1/M2 Mac）
try:
    if torch.backends.mps.is_available():
        device = "mps"  # 使用Apple的Metal Performance Shaders
        print("🍎 检测到MPS设备，将使用Apple Silicon GPU加速")
except:  # noqa: E722
    pass  # 如果MPS不可用，保持之前的设备选择


# ==================== 主推理函数定义 ====================
def main(
    # 模型加载参数
    load_8bit: bool = False,  # 是否使用8位量化加载模型（节省显存但可能影响精度）
    base_model: str = " ",    # 基础LLaMA模型的路径（必需参数）

    # 文件路径参数（注意：这些参数在实际代码中会被覆盖）
    lora_weights: str = "./model/games/1_1024/best_model",  # LoRA权重路径模板
    test_data_path: str = "data/games/test.json",           # 测试数据路径（未使用）
    result_json_data: str = "games.json",                   # 结果文件名模板

    # 推理配置参数
    batch_size: int = 1,      # 批量推理的批次大小（1表示逐个处理）
):
    """
    联邦学习推荐系统主推理函数

    功能说明：
    1. 遍历所有客户端，加载对应的最佳训练模型
    2. 对每个客户端的测试数据进行推理
    3. 生成推荐结果并保存到JSON文件

    参数说明：
    - load_8bit: 是否使用8位量化（推荐False以保证推理质量）
    - base_model: LLaMA基础模型路径，必须指定
    - batch_size: 批处理大小，可根据显存大小调整

    返回值：无（结果直接保存到文件）
    """

    # ==================== 参数验证 ====================
    # 确保用户提供了基础模型路径，这是推理的必要条件
    assert (
        base_model
    ), "❌ 错误：请指定基础模型路径，例如：--base_model='path/to/llama-7b-hf'"

    # ==================== 加载联邦学习的客户端测试数据 ====================
    print("📂 正在加载客户端测试数据...")
    # 这个pickle文件是在训练阶段生成的，包含了按客户端分割的测试数据
    with open('./data/test_client_data.pkl', 'rb') as file:
        test_data_all = pickle.load(file)  # test_data_all是一个列表，每个元素是一个客户端的测试数据

    print(f"📊 成功加载 {len(test_data_all)} 个客户端的测试数据")

    # ==================== 初始化结果存储 ====================
    result = []  # 用于存储所有客户端的推理结果，最终会合并保存
    # ==================== 遍历所有客户端进行推理 ====================
    # 联邦学习的核心特点：每个客户端都有自己独特的模型
    for client_idx in range(len(test_data_all)):
        print(f"\n🔄 开始处理客户端 {client_idx + 1}/{len(test_data_all)}")

        # ==================== 构建客户端模型路径 ====================
        # 每个客户端的最佳模型都保存在独立的目录中
        lora_weights = f'./model/games/1_1024/best_client{client_idx}_model'
        print(f"📁 客户端 {client_idx} 模型路径: {lora_weights}")

        # ==================== 加载分词器 ====================
        # 分词器负责将文本转换为模型可理解的token序列
        print(f"🔤 正在加载分词器...")
        tokenizer = LlamaTokenizer.from_pretrained(base_model)

        # ==================== 根据设备类型加载模型 ====================
        print(f"🤖 正在加载客户端 {client_idx} 的模型...")

        if device == "cuda":
            # CUDA设备（NVIDIA GPU）的模型加载配置
            print("  使用CUDA设备加载模型")
            # 第一步：加载基础LLaMA模型
            model = LlamaForCausalLM.from_pretrained(
                base_model,                    # 基础模型路径
                load_in_8bit=load_8bit,       # 是否使用8位量化
                torch_dtype=torch.float16,    # 使用半精度浮点数节省显存
                device_map="auto",            # 自动分配设备
            )
            # 第二步：加载该客户端的LoRA权重
            model = PeftModel.from_pretrained(
                model,                        # 基础模型
                lora_weights,                 # 客户端特定的LoRA权重路径
                torch_dtype=torch.float16,    # 保持半精度
                device_map={'': 0}            # 指定使用第0号GPU
            )

        elif device == "mps":
            # Apple Silicon设备（M1/M2 Mac）的模型加载配置
            print("  使用MPS设备加载模型")
            model = LlamaForCausalLM.from_pretrained(
                base_model,
                device_map={"": device},      # 指定使用MPS设备
                torch_dtype=torch.float16,   # 使用半精度
            )
            model = PeftModel.from_pretrained(
                model,
                lora_weights,
                device_map={"": device},      # 保持在MPS设备上
                torch_dtype=torch.float16,
            )

        else:
            # CPU设备的模型加载配置
            print("  使用CPU设备加载模型")
            model = LlamaForCausalLM.from_pretrained(
                base_model,
                device_map={"": device},      # 指定使用CPU
                low_cpu_mem_usage=True        # 启用低CPU内存使用模式
            )
            model = PeftModel.from_pretrained(
                model,
                lora_weights,
                device_map={"": device},      # 保持在CPU上
            )

        # ==================== 配置分词器 ====================
        # 设置填充方向为左侧，这对批量推理很重要
        # 左侧填充确保生成的文本从右侧开始，符合语言模型的生成习惯
        tokenizer.padding_side = "left"
        print("  ✅ 分词器配置完成")

        # ==================== 修复模型配置 ====================
        # 这部分代码修复了某些预训练模型可能存在的配置问题
        print("  🔧 正在修复模型配置...")

        # 设置特殊token的ID，这些token在文本生成中有特殊作用
        model.config.pad_token_id = tokenizer.pad_token_id = 0  # 填充token（unk）
        model.config.bos_token_id = 1  # 序列开始token（beginning of sequence）
        model.config.eos_token_id = 2  # 序列结束token（end of sequence）

        # ==================== 模型精度优化 ====================
        if not load_8bit:
            # 如果没有使用8位量化，则手动转换为半精度
            # 这可以解决某些用户遇到的兼容性问题，同时节省显存
            model.half()
            print("  📉 模型已转换为半精度（float16）")

        # ==================== 设置模型为评估模式 ====================
        # 评估模式会关闭dropout等训练时使用的随机性组件
        model.eval()
        print("  🎯 模型已设置为评估模式")

        # ==================== 模型编译优化（可选） ====================
        # PyTorch 2.0的编译功能可以显著提升推理速度
        if torch.__version__ >= "2" and sys.platform != "win32":
            print("  ⚡ 正在编译模型以提升推理速度...")
            model = torch.compile(model)
            print("  ✅ 模型编译完成")

        # ==================== 定义推理评估函数 ====================
        def evaluate(
            instructions,           # 指令列表（如"根据用户历史推荐游戏"）
            inputs=None,           # 输入列表（如用户的游戏历史）
            temperature=0,         # 生成温度（0表示确定性生成，>0表示随机性）
            top_p=0.9,            # 核采样参数（控制生成多样性）
            top_k=40,             # Top-K采样参数（限制候选词数量）
            num_beams=4,          # 束搜索的束数量（平衡质量和速度）
            max_new_tokens=128,   # 最大生成token数量
            **kwargs,             # 其他生成参数
        ):
            """
            批量推理函数 - 推荐系统的核心推理逻辑

            参数说明：
            - instructions: 推荐任务的指令描述
            - inputs: 用户历史行为数据
            - temperature: 控制生成的随机性，0=确定性，1=高随机性
            - top_p: 核采样，保留累积概率达到p的词汇
            - top_k: 只考虑概率最高的k个词汇
            - num_beams: 束搜索数量，越大质量越好但速度越慢

            返回值：
            - 生成的推荐结果列表
            """

            # ==================== 构建输入提示 ====================
            # 将指令和输入组合成完整的提示文本
            # 这是推荐系统的关键步骤：将结构化数据转换为自然语言
            prompt = [generate_prompt(instruction, input) for instruction, input in zip(instructions, inputs)]
            print(f"    📝 构建了 {len(prompt)} 个推理提示")

            # ==================== 文本分词处理 ====================
            # 将文本提示转换为模型可理解的token序列
            inputs = tokenizer(
                prompt,                    # 输入文本列表
                return_tensors="pt",       # 返回PyTorch张量
                padding=True,              # 填充到相同长度
                truncation=True,           # 截断过长序列
                max_length=512             # 最大序列长度
            ).to(device)                   # 移动到指定设备

            # ==================== 配置生成参数 ====================
            # 这些参数控制模型如何生成推荐结果
            generation_config = GenerationConfig(
                temperature=temperature,    # 生成温度（推荐系统通常使用较低温度）
                top_p=top_p,               # 核采样参数
                top_k=top_k,               # Top-K采样参数
                num_beams=num_beams,       # 束搜索参数
                num_return_sequences=1,    # 每个输入返回1个结果
                **kwargs,                  # 其他自定义参数
            )

            # ==================== 执行模型推理 ====================
            # 使用torch.no_grad()禁用梯度计算，节省内存并加速推理
            with torch.no_grad():
                print(f"    🧠 正在进行模型推理...")
                generation_output = model.generate(
                    **inputs,                      # 输入token序列
                    generation_config=generation_config,  # 生成配置
                    return_dict_in_generate=True,  # 返回详细信息
                    output_scores=True,            # 输出生成分数
                    max_new_tokens=max_new_tokens, # 最大新生成token数
                )

            # ==================== 解码生成结果 ====================
            # 将生成的token序列转换回文本
            s = generation_output.sequences  # 获取生成的token序列
            output = tokenizer.batch_decode(s, skip_special_tokens=True)  # 批量解码

            # ==================== 提取推荐结果 ====================
            # 从完整的生成文本中提取推荐部分
            # 推荐结果通常在"Response:"标记之后
            output = [_.split('Response:\n')[-1] for _ in output]
            print(f"    ✅ 成功生成 {len(output)} 个推荐结果")

            # 注释掉的代码：处理多束搜索结果的逻辑（当前未使用）
            # real_outputs = [output[i * num_beams: (i + 1) * num_beams] for i in range(len(output) // num_beams)]
            real_outputs = output
            return real_outputs


        # ==================== 准备客户端测试数据 ====================
        outputs = []  # 存储该客户端的所有推理结果
        from tqdm import tqdm  # 导入进度条库，显示推理进度

        # 获取当前客户端的测试数据
        test_data = test_data_all[client_idx]
        print(f"📊 客户端 {client_idx} 的测试数据量: {len(test_data)}")

        # 注释掉的代码：直接从文件加载测试数据的方式（已被客户端数据替代）
        # with open(test_data_path, 'r') as f:
        #     test_data = json.load(f)

        # 可选：限制测试数据量进行快速测试
        # test_data = test_data[:10]  # 取前10条数据进行测试
        # print(f"限制测试数据量: {len(test_data)}")

        # ==================== 提取指令和输入数据 ====================
        # 从测试数据中分离出指令和输入，为批量推理做准备
        instructions = [_['instruction'] for _ in test_data]  # 提取所有指令
        inputs = [_['input'] for _ in test_data]              # 提取所有输入

        print(f"📋 提取了 {len(instructions)} 条指令和 {len(inputs)} 条输入")

        # ==================== 定义批处理函数 ====================
        def batch(list, batch_size=batch_size):
            """
            将列表分割成指定大小的批次

            这个函数是批量推理的关键，它将大量数据分成小批次处理，
            避免显存溢出，同时提高推理效率。

            参数：
            - list: 要分割的列表
            - batch_size: 每个批次的大小

            返回：
            - 生成器，每次yield一个批次的数据
            """
            chunk_size = (len(list) - 1) // batch_size + 1  # 计算总批次数
            for i in range(chunk_size):
                # 计算当前批次的起始和结束索引
                start_idx = batch_size * i
                end_idx = batch_size * (i + 1)
                yield list[start_idx:end_idx]  # 返回当前批次的数据

        # ==================== 执行批量推理 ====================
        print(f"🚀 开始批量推理，批次大小: {batch_size}")

        # 使用tqdm显示推理进度，让用户了解推理状态
        total_batches = (len(instructions) - 1) // batch_size + 1
        for i, batch_ in tqdm(enumerate(zip(batch(instructions), batch(inputs))),
                             total=total_batches,
                             desc=f"客户端{client_idx}推理进度"):

            # 解包当前批次的指令和输入
            batch_instructions, batch_inputs = batch_

            # 对当前批次进行推理
            batch_output = evaluate(batch_instructions, batch_inputs)

            # 将批次结果添加到总结果中
            outputs = outputs + batch_output

        print(f"✅ 客户端 {client_idx} 推理完成，共生成 {len(outputs)} 个结果")

        # ==================== 将推理结果添加到测试数据 ====================
        try:
            # 为每个测试样本添加预测结果
            print(f"📝 正在为测试数据添加预测结果...")
            for i, test in tqdm(enumerate(test_data), desc="添加预测结果"):
                test_data[i]['predict'] = outputs[i]  # 添加predict字段
        except Exception as e:
            # 如果出现错误，启动调试器进行问题排查
            print(f"❌ 添加预测结果时出错: {e}")
            import ipdb; ipdb.set_trace()  # 调试断点

        # ==================== 保存推理结果 ====================
        # 为每个客户端生成独立的结果文件
        result_json_data = f'games_client{client_idx}.json'
        print(f"💾 正在保存客户端 {client_idx} 的结果到: {result_json_data}")

        if client_idx == 0:
            # 第一个客户端：初始化结果列表
            result = test_data
            with open(result_json_data, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=4, ensure_ascii=False)
        else:
            # 后续客户端：扩展结果列表
            result.extend(test_data)
            with open(result_json_data, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=4, ensure_ascii=False)

        print(f"✅ 客户端 {client_idx} 结果保存完成")

        # ==================== 清理内存 ====================
        # 删除模型以释放GPU内存，为下一个客户端做准备
        print(f"🧹 正在清理客户端 {client_idx} 的模型内存...")
        del model
        torch.cuda.empty_cache()  # 清空CUDA缓存
        print(f"✅ 内存清理完成")

    # ==================== 推理完成总结 ====================
    print(f"\n🎉 所有客户端推理完成！")
    print(f"📊 总共处理了 {len(test_data_all)} 个客户端")
    print(f"📁 结果已保存到 games_client*.json 文件中")
    print(f"🎯 推理任务圆满完成！")


# ==================== 提示生成函数 ====================
def generate_prompt(instruction, input=None):
    """
    生成推荐任务的输入提示

    这个函数是推荐系统的核心组件之一，它将结构化的推荐数据
    转换为大语言模型能理解的自然语言提示格式。

    参数说明：
    - instruction: 推荐任务的指令描述
      例如："根据用户的游戏历史，推荐下一个可能喜欢的游戏"
    - input: 用户的历史行为数据（可选）
      例如："用户玩过：《塞尔达传说》、《超级马里奥》"

    返回值：
    - 格式化的提示文本，遵循Alpaca指令格式

    提示格式说明：
    这里使用的是Alpaca指令格式，包含三个部分：
    1. Instruction: 任务描述
    2. Input: 输入信息（可选）
    3. Response: 期望的输出（推理时为空，等待模型填充）
    """

    if input:
        # 有输入信息的情况：包含指令、输入和响应三部分
        return f"""Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.

### Instruction:
{instruction}

### Input:
{input}

### Response:
"""
    else:
        # 无输入信息的情况：只包含指令和响应两部分
        return f"""Below is an instruction that describes a task. Write a response that appropriately completes the request.

### Instruction:
{instruction}

### Response:
"""


# ==================== 程序入口点 ====================
if __name__ == "__main__":
    """
    脚本入口点

    使用Fire库将main函数转换为命令行接口，支持以下调用方式：

    基本用法：
    python inference.py --base_model "path/to/llama-7b"

    完整参数：
    python inference.py \
        --base_model "path/to/llama-7b" \
        --load_8bit False \
        --batch_size 4

    参数说明：
    - base_model: LLaMA基础模型路径（必需）
    - load_8bit: 是否使用8位量化（默认False）
    - batch_size: 批处理大小（默认1）
    """
    print("🚀 启动FELLRec联邦学习推荐系统推理脚本...")
    fire.Fire(main)
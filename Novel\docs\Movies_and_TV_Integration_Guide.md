# Movies_and_TV 数据集集成指南

本文档详细说明了如何在Novel项目中集成Movies_and_TV数据集，以及如何与LLM-SRec项目进行对比实验。

## 📋 目录

1. [数据集概述](#数据集概述)
2. [集成步骤](#集成步骤)
3. [数据格式说明](#数据格式说明)
4. [使用方法](#使用方法)
5. [对比实验](#对比实验)
6. [故障排除](#故障排除)

## 📊 数据集概述

Movies_and_TV数据集是从LLM-SRec项目中提取的电影和电视节目推荐数据集，包含：

### 数据文件
- `Movies_and_TV_train.txt` - 训练数据 (128,695条交互)
- `Movies_and_TV_valid.txt` - 验证数据 (7,911条交互)
- `Movies_and_TV_test.txt` - 测试数据 (7,468条交互)
- `text_name_dict.json.gz` - 物品文本信息字典
- `Results.txt` - LLM-SRec基准结果

### 数据统计
- **总用户数**: 约7,000+
- **总物品数**: 约16,000+
- **总交互数**: 约144,000+
- **平均序列长度**: 约18.3
- **数据稀疏度**: 高度稀疏

## 🔧 集成步骤

### 步骤1: 数据集复制
数据集已自动复制到Novel项目的数据目录：
```
Novel/data/Movies_and_TV/
├── Movies_and_TV_train.txt
├── Movies_and_TV_valid.txt
├── Movies_and_TV_test.txt
├── text_name_dict.json.gz
└── Results.txt
```

### 步骤2: 数据预处理
运行数据预处理脚本验证数据完整性：

```bash
# 验证数据完整性
python scripts/preprocess_movies_tv.py --validate_only

# 生成详细统计信息
python scripts/preprocess_movies_tv.py --generate_stats

# 生成LLM-SRec兼容格式
python scripts/preprocess_movies_tv.py --llm_srec_format
```

### 步骤3: 配置更新
更新Novel项目配置文件以支持Movies_and_TV数据集：

```yaml
# config/collaborative_config.yaml
data:
  dataset: "Movies_and_TV"
  data_dir: "./data"
  max_sequence_length: 128
```

## 📝 数据格式说明

### 交互数据格式
每个数据文件（train/valid/test）采用简单的两列格式：
```
user_id item_id
1 1
1 2
1 3
2 9
2 10
```

### 文本字典格式
`text_name_dict.json.gz` 包含物品的文本信息：
```python
{
    'title': {item_id: title_text, ...},
    'description': {item_id: description_text, ...},
    'time': {item_id: timestamp, ...}
}
```

### 兼容性保证
- **用户ID**: 连续整数，从1开始
- **物品ID**: 连续整数，从1开始
- **序列顺序**: 按时间顺序排列
- **数据分割**: 与LLM-SRec完全一致

## 🚀 使用方法

### 基本数据加载
```python
from utils.data_utils import RecommendationDataLoader

# 配置数据加载器
config = {
    'dataset': 'Movies_and_TV',
    'data_dir': './data',
    'max_sequence_length': 128
}

# 初始化数据加载器
data_loader = RecommendationDataLoader(config)

# 获取数据加载器
train_loader = data_loader.get_train_loader(batch_size=32)
val_loader = data_loader.get_val_loader(batch_size=32)
test_loader = data_loader.get_test_loader(batch_size=32)
```

### 数据预处理
```python
from utils.movies_tv_preprocessor import MoviesAndTVPreprocessor

# 初始化预处理器
preprocessor = MoviesAndTVPreprocessor('./data/Movies_and_TV')

# 验证数据完整性
is_valid = preprocessor.validate_data_integrity()

# 加载和分析数据
stats = preprocessor.load_and_analyze_data()

# 生成兼容格式
preprocessor.generate_llm_srec_compatible_format()
```

### 模型训练示例
```python
from training.collaborative_trainer import CollaborativeTrainer

# 加载配置
with open('config/collaborative_config.yaml', 'r') as f:
    config = yaml.safe_load(f)

# 初始化训练器
trainer = CollaborativeTrainer(config)

# 开始训练
trainer.train()
```

## 🔬 对比实验

### 实验配置
创建对比实验配置文件：

```yaml
# experiments/configs/movies_tv_comparison.yaml
experiment_name: "Novel_vs_LLM-SRec_Movies_and_TV"
dataset_name: "Movies_and_TV"
data_dir: "./data"
max_sequence_length: 128
batch_size: 32
k_values: [5, 10, 20]
metrics: ["ndcg", "recall", "precision", "hit_rate", "mrr"]
output_dir: "./experiments/results/movies_tv_comparison"
```

### 运行对比实验
```bash
# 运行完整对比实验
python scripts/run_comparison_experiment.py \
    --config experiments/configs/movies_tv_comparison.yaml \
    --llm_srec_results ./data/Movies_and_TV/Results.txt \
    --model_checkpoint ./models/novel_best.pth

# 仅验证配置（干运行）
python scripts/run_comparison_experiment.py \
    --config experiments/configs/movies_tv_comparison.yaml \
    --dry_run
```

### 评估指标
对比实验使用与LLM-SRec完全一致的评估指标：

- **NDCG@K**: 归一化折扣累积增益
- **Recall@K**: 召回率
- **Precision@K**: 精确率
- **Hit Rate@K**: 命中率
- **MRR**: 平均倒数排名

### 结果分析
实验完成后，会生成以下文件：
- `experiment_report.md` - 详细实验报告
- `experiment_results.json` - 原始结果数据
- `experiment_config.json` - 实验配置备份

## 🛠️ 故障排除

### 常见问题

#### 1. 数据文件缺失
**错误**: `FileNotFoundError: Required data file not found`

**解决方案**:
```bash
# 检查数据文件是否存在
ls -la Novel/data/Movies_and_TV/

# 重新复制数据文件
cp LLM-SRec-master/SeqRec/data_Movies_and_TV/* Novel/data/Movies_and_TV/
```

#### 2. 文本字典加载失败
**错误**: `Failed to load text dictionary`

**解决方案**:
```python
# 检查文件格式
import pickle
with open('Novel/data/Movies_and_TV/text_name_dict.json.gz', 'rb') as f:
    data = pickle.load(f)
    print(f"Loaded {len(data)} items")
```

#### 3. 内存不足
**错误**: `CUDA out of memory` 或 `MemoryError`

**解决方案**:
```yaml
# 减少批次大小
batch_size: 16  # 或更小

# 减少序列长度
max_sequence_length: 64
```

#### 4. 评估指标不一致
**问题**: 与LLM-SRec结果差异过大

**检查项**:
- 数据预处理是否一致
- 评估指标计算是否正确
- 随机种子是否固定
- 数据分割是否相同

### 调试技巧

#### 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### 数据验证
```python
# 验证数据统计信息
from utils.data_utils import RecommendationDataLoader
data_loader = RecommendationDataLoader(config)
stats = data_loader.get_dataset_stats()
print(stats)
```

#### 小规模测试
```python
# 使用小批次进行测试
test_config = config.copy()
test_config['batch_size'] = 4
```

## 📚 参考资料

- [LLM-SRec项目文档](../LLM-SRec-master/README.md)
- [Novel项目架构说明](../README.md)
- [协同训练配置指南](../config/collaborative_config.yaml)
- [评估指标详细说明](./evaluation_metrics.md)

## 🤝 贡献指南

如果您在使用过程中发现问题或有改进建议，请：

1. 检查现有的[Issues](https://github.com/your-repo/issues)
2. 创建新的Issue描述问题
3. 提交Pull Request进行改进

## 📄 许可证

本集成遵循Novel项目的MIT许可证。Movies_and_TV数据集的使用请遵循原始数据集的许可条款。

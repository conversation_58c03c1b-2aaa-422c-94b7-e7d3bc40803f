"""
Novel vs LLM-SRec 对比实验框架

提供完整的对比实验框架，确保实验设置的一致性和结果的可比性。

主要功能：
1. 统一的实验配置管理
2. 相同的数据预处理流程
3. 一致的评估指标计算
4. 结果对比和可视化
5. 实验报告生成
"""

import os
import sys
import json
import yaml
import time
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import numpy as np
import torch

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.evaluation import RecommendationEvaluator, compare_with_llm_srec
from utils.data_utils import RecommendationDataLoader

logger = logging.getLogger(__name__)


@dataclass
class ExperimentConfig:
    """实验配置类"""
    experiment_name: str
    dataset_name: str = "Movies_and_TV"
    
    # 数据配置
    data_dir: str = "./data"
    max_sequence_length: int = 128
    batch_size: int = 32
    
    # 评估配置
    k_values: List[int] = None
    metrics: List[str] = None
    
    # 模型配置
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    seed: int = 42
    
    # 输出配置
    output_dir: str = "./experiments/results"
    save_predictions: bool = True
    save_detailed_metrics: bool = True
    
    def __post_init__(self):
        if self.k_values is None:
            self.k_values = [5, 10, 20]
        if self.metrics is None:
            self.metrics = ["ndcg", "recall", "precision", "hit_rate", "mrr"]


class ComparisonFramework:
    """
    对比实验框架
    
    提供Novel项目与LLM-SRec项目的标准化对比实验流程。
    """
    
    def __init__(self, config: ExperimentConfig):
        """
        初始化对比实验框架
        
        Args:
            config: 实验配置
        """
        self.config = config
        self.setup_experiment()
        
        # 初始化组件
        self.data_loader = None
        self.evaluator = None
        self.results = {}
        
        logger.info(f"对比实验框架初始化完成: {config.experiment_name}")
    
    def setup_experiment(self):
        """设置实验环境"""
        # 设置随机种子
        torch.manual_seed(self.config.seed)
        np.random.seed(self.config.seed)
        
        # 创建输出目录
        os.makedirs(self.config.output_dir, exist_ok=True)
        
        # 保存实验配置
        config_file = os.path.join(self.config.output_dir, 'experiment_config.json')
        with open(config_file, 'w') as f:
            json.dump(self.config.__dict__, f, indent=2)
        
        logger.info(f"实验环境设置完成，输出目录: {self.config.output_dir}")
    
    def load_data(self):
        """加载数据集"""
        logger.info("加载数据集...")
        
        data_config = {
            'dataset': self.config.dataset_name,
            'data_dir': self.config.data_dir,
            'max_sequence_length': self.config.max_sequence_length
        }
        
        self.data_loader = RecommendationDataLoader(data_config)
        
        # 获取数据集统计信息
        stats = self.data_loader.get_dataset_stats()
        self.results['dataset_stats'] = stats
        
        logger.info(f"数据集加载完成: {stats}")
    
    def initialize_evaluator(self):
        """初始化评估器"""
        self.evaluator = RecommendationEvaluator(k_values=self.config.k_values)
        logger.info(f"评估器初始化完成，K值: {self.config.k_values}")
    
    def run_novel_experiment(self, model, model_name: str = "Novel") -> Dict[str, float]:
        """
        运行Novel模型实验
        
        Args:
            model: Novel模型实例
            model_name: 模型名称
            
        Returns:
            评估结果字典
        """
        logger.info(f"开始运行{model_name}模型实验...")
        
        # 重置评估器
        self.evaluator.reset_metrics()
        
        # 获取测试数据加载器
        test_loader = self.data_loader.get_test_loader(
            batch_size=self.config.batch_size,
            shuffle=False
        )
        
        model.eval()
        start_time = time.time()
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(test_loader):
                # 模型预测
                predictions = model.predict(batch)
                
                # 准备目标标签
                targets = self._prepare_targets(batch)
                
                # 评估批次
                self.evaluator.evaluate_batch(predictions, targets)
                
                if batch_idx % 100 == 0:
                    logger.info(f"已处理 {batch_idx + 1}/{len(test_loader)} 个批次")
        
        end_time = time.time()
        
        # 获取评估结果
        metrics = self.evaluator.get_average_metrics()
        detailed_metrics = self.evaluator.get_detailed_metrics()
        
        # 保存结果
        result = {
            'model_name': model_name,
            'metrics': metrics,
            'detailed_metrics': detailed_metrics,
            'inference_time': end_time - start_time,
            'num_samples': len(test_loader.dataset)
        }
        
        self.results[model_name.lower()] = result
        
        logger.info(f"{model_name}模型实验完成")
        self.evaluator.print_metrics()
        
        return metrics
    
    def load_llm_srec_results(self, results_file: str) -> Dict[str, float]:
        """
        加载LLM-SRec的实验结果
        
        Args:
            results_file: LLM-SRec结果文件路径
            
        Returns:
            LLM-SRec评估结果字典
        """
        logger.info(f"加载LLM-SRec结果: {results_file}")
        
        if not os.path.exists(results_file):
            logger.warning(f"LLM-SRec结果文件不存在: {results_file}")
            return {}
        
        try:
            with open(results_file, 'r') as f:
                llm_srec_results = json.load(f)
            
            # 标准化指标名称格式
            standardized_results = self._standardize_metric_names(llm_srec_results)
            
            self.results['llm_srec'] = {
                'model_name': 'LLM-SRec',
                'metrics': standardized_results,
                'source_file': results_file
            }
            
            logger.info("LLM-SRec结果加载完成")
            return standardized_results
            
        except Exception as e:
            logger.error(f"加载LLM-SRec结果失败: {e}")
            return {}
    
    def _standardize_metric_names(self, results: Dict[str, Any]) -> Dict[str, float]:
        """
        标准化指标名称格式
        
        Args:
            results: 原始结果字典
            
        Returns:
            标准化后的结果字典
        """
        standardized = {}
        
        # 定义指标名称映射
        metric_mapping = {
            'NDCG@5': 'ndcg@5', 'NDCG@10': 'ndcg@10', 'NDCG@20': 'ndcg@20',
            'Recall@5': 'recall@5', 'Recall@10': 'recall@10', 'Recall@20': 'recall@20',
            'Precision@5': 'precision@5', 'Precision@10': 'precision@10', 'Precision@20': 'precision@20',
            'HitRate@5': 'hit_rate@5', 'HitRate@10': 'hit_rate@10', 'HitRate@20': 'hit_rate@20',
            'MRR': 'mrr'
        }
        
        for original_name, standard_name in metric_mapping.items():
            if original_name in results:
                standardized[standard_name] = results[original_name]
        
        return standardized
    
    def _prepare_targets(self, batch: Dict[str, torch.Tensor]) -> List[List[int]]:
        """
        准备目标标签
        
        Args:
            batch: 批次数据
            
        Returns:
            目标标签列表
        """
        # 这里需要根据具体的数据格式来实现
        # 暂时返回空列表，实际使用时需要根据数据格式调整
        batch_size = batch['user_ids'].size(0)
        return [[] for _ in range(batch_size)]
    
    def compare_results(self) -> Dict[str, Any]:
        """
        对比实验结果
        
        Returns:
            对比结果字典
        """
        logger.info("开始对比实验结果...")
        
        if 'novel' not in self.results or 'llm_srec' not in self.results:
            logger.error("缺少对比所需的实验结果")
            return {}
        
        novel_metrics = self.results['novel']['metrics']
        llm_srec_metrics = self.results['llm_srec']['metrics']
        
        # 使用评估模块的对比函数
        comparison = compare_with_llm_srec(novel_metrics, llm_srec_metrics)
        
        self.results['comparison'] = comparison
        
        logger.info("结果对比完成")
        return comparison
    
    def generate_report(self) -> str:
        """
        生成实验报告
        
        Returns:
            报告文件路径
        """
        logger.info("生成实验报告...")
        
        report_file = os.path.join(self.config.output_dir, 'experiment_report.md')
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(self._generate_report_content())
        
        logger.info(f"实验报告已生成: {report_file}")
        return report_file
    
    def _generate_report_content(self) -> str:
        """生成报告内容"""
        content = []
        
        # 报告标题
        content.append(f"# {self.config.experiment_name} 实验报告\n")
        content.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        # 实验配置
        content.append("## 实验配置\n")
        content.append(f"- 数据集: {self.config.dataset_name}")
        content.append(f"- 最大序列长度: {self.config.max_sequence_length}")
        content.append(f"- 批次大小: {self.config.batch_size}")
        content.append(f"- 评估K值: {self.config.k_values}")
        content.append(f"- 随机种子: {self.config.seed}\n")
        
        # 数据集统计
        if 'dataset_stats' in self.results:
            stats = self.results['dataset_stats']
            content.append("## 数据集统计\n")
            content.append(f"- 训练用户数: {stats.get('train_users', 'N/A'):,}")
            content.append(f"- 验证用户数: {stats.get('valid_users', 'N/A'):,}")
            content.append(f"- 测试用户数: {stats.get('test_users', 'N/A'):,}")
            content.append(f"- 总物品数: {stats.get('total_items', 'N/A'):,}")
            content.append(f"- 最大物品ID: {stats.get('max_item_id', 'N/A'):,}\n")
        
        # 实验结果
        content.append("## 实验结果\n")
        
        if 'novel' in self.results:
            content.append("### Novel模型结果\n")
            metrics = self.results['novel']['metrics']
            for metric_name, value in metrics.items():
                content.append(f"- {metric_name}: {value:.4f}")
            content.append("")
        
        if 'llm_srec' in self.results:
            content.append("### LLM-SRec模型结果\n")
            metrics = self.results['llm_srec']['metrics']
            for metric_name, value in metrics.items():
                content.append(f"- {metric_name}: {value:.4f}")
            content.append("")
        
        # 对比结果
        if 'comparison' in self.results:
            comparison = self.results['comparison']
            content.append("### 对比结果\n")
            
            if 'improvements' in comparison:
                content.append("#### 改进幅度 (%)\n")
                for metric_name, improvement in comparison['improvements'].items():
                    status = "📈" if improvement > 0 else "📉"
                    content.append(f"- {metric_name}: {status} {improvement:+.2f}%")
                content.append("")
            
            if 'summary' in comparison:
                summary = comparison['summary']
                content.append("#### 总结\n")
                content.append(f"- 平均改进幅度: {summary.get('average_improvement', 0):.2f}%")
                content.append(f"- 最佳指标: {summary.get('best_metric', 'N/A')}")
                content.append(f"- 最差指标: {summary.get('worst_metric', 'N/A')}\n")
        
        return "\n".join(content)
    
    def save_results(self):
        """保存实验结果"""
        results_file = os.path.join(self.config.output_dir, 'experiment_results.json')
        
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        logger.info(f"实验结果已保存: {results_file}")
    
    def run_complete_comparison(self, novel_model, llm_srec_results_file: str):
        """
        运行完整的对比实验
        
        Args:
            novel_model: Novel模型实例
            llm_srec_results_file: LLM-SRec结果文件路径
        """
        logger.info("开始完整对比实验...")
        
        # 1. 加载数据
        self.load_data()
        
        # 2. 初始化评估器
        self.initialize_evaluator()
        
        # 3. 运行Novel实验
        self.run_novel_experiment(novel_model)
        
        # 4. 加载LLM-SRec结果
        self.load_llm_srec_results(llm_srec_results_file)
        
        # 5. 对比结果
        self.compare_results()
        
        # 6. 生成报告
        self.generate_report()
        
        # 7. 保存结果
        self.save_results()
        
        logger.info("完整对比实验完成！")


def create_experiment_config(experiment_name: str, **kwargs) -> ExperimentConfig:
    """
    创建实验配置的便捷函数

    Args:
        experiment_name: 实验名称
        **kwargs: 其他配置参数

    Returns:
        实验配置对象
    """
    return ExperimentConfig(experiment_name=experiment_name, **kwargs)


def load_config_from_yaml(config_file: str) -> ExperimentConfig:
    """
    从YAML文件加载实验配置

    Args:
        config_file: 配置文件路径

    Returns:
        实验配置对象
    """
    with open(config_file, 'r') as f:
        config_dict = yaml.safe_load(f)

    return ExperimentConfig(**config_dict)

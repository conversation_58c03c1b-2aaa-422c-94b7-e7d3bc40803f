#!/usr/bin/env python3
"""
数据加载测试脚本

验证Movies_and_TV数据集的加载和预处理功能是否正常工作。
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from utils.data_utils import RecommendationDataLoader
from utils.evaluation import RecommendationEvaluator
import torch
import numpy as np

def test_data_loading():
    """测试数据加载功能"""
    print("🔍 测试数据加载功能...")
    
    # 配置数据加载器
    config = {
        'dataset': 'Movies_and_TV',
        'data_dir': './data',
        'max_sequence_length': 128
    }
    
    try:
        # 初始化数据加载器
        data_loader = RecommendationDataLoader(config)
        print("✅ 数据加载器初始化成功")
        
        # 获取数据集统计信息
        stats = data_loader.get_dataset_stats()
        print(f"📊 数据集统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value:,}" if isinstance(value, int) else f"  {key}: {value}")
        
        # 测试训练数据加载器
        train_loader = data_loader.get_train_loader(batch_size=4, shuffle=False)
        print(f"📚 训练数据加载器: {len(train_loader)} 个批次")
        
        # 获取一个批次进行测试
        for batch_idx, batch in enumerate(train_loader):
            print(f"🔍 测试批次 {batch_idx + 1}:")
            print(f"  用户ID形状: {batch['user_ids'].shape}")
            print(f"  序列形状: {batch['sequences'].shape}")
            print(f"  序列长度形状: {batch['sequence_lengths'].shape}")
            print(f"  用户ID示例: {batch['user_ids'][:3].tolist()}")
            print(f"  序列长度示例: {batch['sequence_lengths'][:3].tolist()}")
            
            if batch_idx >= 2:  # 只测试前3个批次
                break
        
        print("✅ 数据加载测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_evaluation():
    """测试评估功能"""
    print("\n🔍 测试评估功能...")
    
    try:
        # 初始化评估器
        evaluator = RecommendationEvaluator([5, 10, 20])
        print("✅ 评估器初始化成功")
        
        # 生成模拟数据
        batch_size = 10
        num_items = 100
        
        # 模拟预测分数（随机）
        predictions = np.random.rand(batch_size, num_items)
        
        # 模拟真实标签（每个用户有3-5个真实物品）
        targets = []
        for i in range(batch_size):
            num_targets = np.random.randint(3, 6)
            target_items = np.random.choice(num_items, num_targets, replace=False).tolist()
            targets.append(target_items)
        
        print(f"📊 模拟数据:")
        print(f"  预测形状: {predictions.shape}")
        print(f"  目标数量: {len(targets)}")
        print(f"  目标示例: {targets[:3]}")
        
        # 评估批次
        metrics = evaluator.evaluate_batch(predictions, targets)
        
        print(f"📈 评估结果:")
        for metric_name, value in metrics.items():
            print(f"  {metric_name}: {value:.4f}")
        
        # 打印详细指标
        evaluator.print_metrics(detailed=False)
        
        print("✅ 评估功能测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 评估功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """测试集成功能"""
    print("\n🔍 测试集成功能...")
    
    try:
        # 配置
        config = {
            'dataset': 'Movies_and_TV',
            'data_dir': './data',
            'max_sequence_length': 128
        }
        
        # 数据加载器
        data_loader = RecommendationDataLoader(config)
        test_loader = data_loader.get_test_loader(batch_size=8, shuffle=False)
        
        # 评估器
        evaluator = RecommendationEvaluator([5, 10])
        
        print("🔄 运行集成测试...")
        
        # 模拟模型预测过程
        num_batches_to_test = 3
        for batch_idx, batch in enumerate(test_loader):
            if batch_idx >= num_batches_to_test:
                break
                
            batch_size = batch['user_ids'].size(0)
            num_items = 1000  # 假设有1000个物品
            
            # 模拟模型预测
            predictions = torch.randn(batch_size, num_items)
            
            # 模拟目标标签
            targets = []
            for i in range(batch_size):
                # 从用户序列中选择一些物品作为目标
                user_sequence = batch['sequences'][i]
                valid_items = user_sequence[user_sequence > 0].tolist()
                if valid_items:
                    # 随机选择1-3个物品作为目标
                    num_targets = min(len(valid_items), np.random.randint(1, 4))
                    target_items = np.random.choice(valid_items, num_targets, replace=False).tolist()
                else:
                    target_items = [1, 2, 3]  # 默认目标
                targets.append(target_items)
            
            # 评估
            evaluator.evaluate_batch(predictions, targets)
            
            print(f"  处理批次 {batch_idx + 1}/{num_batches_to_test}")
        
        # 获取最终结果
        final_metrics = evaluator.get_average_metrics()
        
        print(f"📊 集成测试结果:")
        for metric_name, value in final_metrics.items():
            print(f"  {metric_name}: {value:.4f}")
        
        print("✅ 集成功能测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 集成功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始Movies_and_TV数据集集成测试")
    print("=" * 50)
    
    # 检查数据文件是否存在
    data_files = [
        './data/Movies_and_TV/Movies_and_TV_train.txt',
        './data/Movies_and_TV/Movies_and_TV_valid.txt',
        './data/Movies_and_TV/Movies_and_TV_test.txt',
        './data/Movies_and_TV/text_name_dict.json.gz'
    ]
    
    print("📁 检查数据文件...")
    missing_files = []
    for file_path in data_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ 缺少数据文件: {missing_files}")
        print("请确保已正确复制Movies_and_TV数据集")
        return 1
    
    # 运行测试
    tests = [
        ("数据加载", test_data_loading),
        ("评估功能", test_evaluation),
        ("集成功能", test_integration)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed_tests += 1
        else:
            print(f"❌ {test_name}测试失败")
    
    # 总结
    print("\n" + "=" * 50)
    print(f"📊 测试总结: {passed_tests}/{total_tests} 个测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！Movies_and_TV数据集集成成功！")
        print("\n📝 下一步:")
        print("1. 运行完整的数据预处理: python scripts/preprocess_movies_tv.py --llm_srec_format")
        print("2. 创建实验配置: python scripts/run_comparison_experiment.py")
        print("3. 运行对比实验: python scripts/run_comparison_experiment.py --config experiments/configs/movies_tv_comparison.yaml")
        return 0
    else:
        print("❌ 部分测试失败，请检查错误信息并修复问题")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

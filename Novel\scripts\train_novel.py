#!/usr/bin/env python3
"""
Novel端云协同推荐系统训练脚本

支持多数据集的统一训练接口，采用与LLM-SRec相似的命令行参数设计。

使用方法：
    # 训练Movies_and_TV数据集
    python scripts/train_novel.py --dataset Movies_and_TV --device 0 --train
    
    # 训练Industrial_and_Scientific数据集
    python scripts/train_novel.py --dataset Industrial_and_Scientific --device 0 --train
    
    # 使用配置文件训练
    python scripts/train_novel.py --config config/collaborative_config.yaml --train
    
    # 仅评估模式
    python scripts/train_novel.py --dataset Movies_and_TV --eval

功能：
1. 支持多数据集的统一训练
2. 端云协同的大小模型训练
3. 与LLM-SRec兼容的参数接口
4. 灵活的配置管理
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.config_manager import ConfigManager, create_argument_parser, parse_args_and_config
from utils.data_utils import create_data_loader, list_available_datasets
import torch
import numpy as np
import random

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('novel_training.log')
    ]
)

logger = logging.getLogger(__name__)


def set_seed(seed: int):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def setup_device(device_str: str) -> torch.device:
    """设置计算设备"""
    if device_str == 'cpu':
        return torch.device('cpu')
    elif device_str.isdigit():
        if torch.cuda.is_available():
            return torch.device(f'cuda:{device_str}')
        else:
            logger.warning("CUDA not available, using CPU")
            return torch.device('cpu')
    else:
        return torch.device(device_str)


def train_model(config_manager: ConfigManager):
    """
    训练Novel模型
    
    Args:
        config_manager: 配置管理器
    """
    config = config_manager.get_config()
    
    logger.info("开始训练Novel端云协同推荐系统")
    logger.info(f"实验名称: {config['experiment_name']}")
    logger.info(f"数据集: {config['data']['dataset']}")
    logger.info(f"设备: {config['device']}")
    
    # 设置随机种子
    set_seed(config['seed'])
    
    # 设置设备
    device = setup_device(config['device'])
    logger.info(f"使用设备: {device}")
    
    # 加载数据
    logger.info("加载数据集...")
    data_config = config_manager.get_data_config()
    data_loader = create_data_loader(data_config['dataset'], data_config)
    
    # 获取数据统计信息
    stats = data_loader.get_dataset_stats()
    logger.info(f"数据集统计信息:")
    for key, value in stats.items():
        logger.info(f"  {key}: {value}")
    
    # 获取数据加载器
    train_config = config_manager.get_training_config()
    train_dataloader = data_loader.get_train_loader(
        batch_size=train_config['batch_size'],
        shuffle=True
    )
    val_dataloader = data_loader.get_val_loader(
        batch_size=train_config['batch_size'],
        shuffle=False
    )
    
    logger.info(f"训练批次数: {len(train_dataloader)}")
    logger.info(f"验证批次数: {len(val_dataloader)}")
    
    # TODO: 初始化Novel模型
    # 这里需要根据实际的Novel模型实现来完成
    logger.info("初始化Novel模型...")
    logger.warning("Novel模型实现尚未完成，这里是模拟训练过程")
    
    # 模拟训练过程
    num_epochs = train_config['num_epochs']
    for epoch in range(num_epochs):
        logger.info(f"Epoch {epoch + 1}/{num_epochs}")
        
        # 模拟训练步骤
        for batch_idx, batch in enumerate(train_dataloader):
            if batch_idx >= 3:  # 只模拟前3个批次
                break
            logger.info(f"  训练批次 {batch_idx + 1}: "
                       f"用户数={batch['user_ids'].size(0)}, "
                       f"序列长度={batch['sequences'].size(1)}")
        
        # 模拟验证步骤
        if epoch % 5 == 0:  # 每5个epoch验证一次
            logger.info(f"  验证 Epoch {epoch + 1}")
            for batch_idx, batch in enumerate(val_dataloader):
                if batch_idx >= 1:  # 只模拟1个验证批次
                    break
                logger.info(f"    验证批次 {batch_idx + 1}: "
                           f"用户数={batch['user_ids'].size(0)}")
    
    logger.info("训练完成！")


def evaluate_model(config_manager: ConfigManager):
    """
    评估Novel模型
    
    Args:
        config_manager: 配置管理器
    """
    config = config_manager.get_config()
    
    logger.info("开始评估Novel端云协同推荐系统")
    logger.info(f"数据集: {config['data']['dataset']}")
    
    # 加载数据
    data_config = config_manager.get_data_config()
    data_loader = create_data_loader(data_config['dataset'], data_config)
    
    # 获取测试数据加载器
    eval_config = config.get('evaluation', {})
    test_dataloader = data_loader.get_test_loader(
        batch_size=eval_config.get('batch_size', 64),
        shuffle=False
    )
    
    logger.info(f"测试批次数: {len(test_dataloader)}")
    
    # TODO: 加载训练好的模型并进行评估
    logger.warning("Novel模型评估实现尚未完成，这里是模拟评估过程")
    
    # 模拟评估过程
    k_values = eval_config.get('k_values', [5, 10, 20])
    metrics = eval_config.get('metrics', ['ndcg', 'recall', 'precision'])
    
    logger.info(f"评估指标: {metrics}")
    logger.info(f"K值: {k_values}")
    
    # 模拟评估结果
    for batch_idx, batch in enumerate(test_dataloader):
        if batch_idx >= 2:  # 只模拟前2个批次
            break
        logger.info(f"评估批次 {batch_idx + 1}: "
                   f"用户数={batch['user_ids'].size(0)}")
    
    # 模拟评估结果
    logger.info("评估结果:")
    for metric in metrics:
        for k in k_values:
            # 生成模拟结果
            value = np.random.uniform(0.1, 0.3)
            logger.info(f"  {metric}@{k}: {value:.4f}")
    
    logger.info("评估完成！")


def main():
    """主函数"""
    # 创建参数解析器
    parser = create_argument_parser()

    # 添加训练特定参数（避免冲突）
    parser.add_argument('--mode', type=str, choices=['train', 'eval'], help='运行模式：train或eval')
    parser.add_argument('--list_datasets', action='store_true', help='列出支持的数据集')
    
    args = parser.parse_args()
    
    # 列出支持的数据集
    if args.list_datasets:
        print("支持的数据集:")
        for dataset in list_available_datasets():
            print(f"  - {dataset}")
        return 0
    
    try:
        # 解析配置
        config_manager = parse_args_and_config(args)
        
        # 验证配置
        if not config_manager.validate_config():
            logger.error("配置验证失败")
            return 1
        
        # 根据模式执行相应操作
        if args.mode == 'train':
            train_model(config_manager)
        elif args.mode == 'eval':
            evaluate_model(config_manager)
        else:
            logger.error("请指定 --mode train 或 --mode eval")
            return 1
        
        return 0
        
    except Exception as e:
        logger.error(f"执行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

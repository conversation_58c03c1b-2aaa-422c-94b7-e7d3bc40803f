"""
Movies_and_TV数据集预处理器

专门用于处理Movies_and_TV数据集，确保与LLM-SRec项目的数据处理流程完全一致，
以便进行公平的对比实验。

主要功能：
1. 数据格式验证和转换
2. 序列数据预处理
3. 物品文本信息处理
4. 生成对比实验所需的数据格式
"""

import os
import pickle
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict, Counter
import logging
from tqdm import tqdm

logger = logging.getLogger(__name__)


class MoviesAndTVPreprocessor:
    """
    Movies_and_TV数据集预处理器
    
    确保数据处理流程与LLM-SRec项目完全一致，
    支持生成对比实验所需的各种数据格式。
    """
    
    def __init__(self, data_dir: str, output_dir: str = None):
        """
        初始化预处理器
        
        Args:
            data_dir: Movies_and_TV数据集目录
            output_dir: 输出目录，默认为data_dir/processed
        """
        self.data_dir = data_dir
        self.output_dir = output_dir or os.path.join(data_dir, '..', 'processed')
        
        # 数据文件路径
        self.train_file = os.path.join(data_dir, 'Movies_and_TV_train.txt')
        self.valid_file = os.path.join(data_dir, 'Movies_and_TV_valid.txt')
        self.test_file = os.path.join(data_dir, 'Movies_and_TV_test.txt')
        self.text_dict_file = os.path.join(data_dir, 'text_name_dict.json.gz')
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 数据统计信息
        self.stats = {}
        
        logger.info(f"Preprocessor initialized for {data_dir}")
    
    def load_and_analyze_data(self) -> Dict[str, Any]:
        """
        加载并分析数据集
        
        Returns:
            数据集分析结果
        """
        logger.info("Loading and analyzing Movies_and_TV dataset...")
        
        # 加载各个数据文件
        train_data = self._load_interaction_file(self.train_file)
        valid_data = self._load_interaction_file(self.valid_file)
        test_data = self._load_interaction_file(self.test_file)
        
        # 加载文本字典
        text_dict = self._load_text_dict()
        
        # 统计分析
        self.stats = self._compute_statistics(train_data, valid_data, test_data, text_dict)
        
        logger.info("Data analysis completed")
        return self.stats
    
    def _load_interaction_file(self, file_path: str) -> List[Tuple[int, int]]:
        """
        加载交互数据文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            List[(user_id, item_id)]: 交互数据列表
        """
        interactions = []
        
        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    user_id, item_id = map(int, line.split())
                    interactions.append((user_id, item_id))
        
        logger.info(f"Loaded {len(interactions)} interactions from {file_path}")
        return interactions
    
    def _load_text_dict(self) -> Optional[Dict]:
        """
        加载物品文本字典
        
        Returns:
            物品文本字典
        """
        if not os.path.exists(self.text_dict_file):
            logger.warning(f"Text dictionary file not found: {self.text_dict_file}")
            return None
        
        try:
            with open(self.text_dict_file, 'rb') as f:
                text_dict = pickle.load(f)
            logger.info(f"Loaded text dictionary with {len(text_dict.get('title', {}))} items")
            return text_dict
        except Exception as e:
            logger.error(f"Failed to load text dictionary: {e}")
            return None
    
    def _compute_statistics(self, train_data: List[Tuple[int, int]], 
                          valid_data: List[Tuple[int, int]], 
                          test_data: List[Tuple[int, int]], 
                          text_dict: Optional[Dict]) -> Dict[str, Any]:
        """
        计算数据集统计信息
        
        Args:
            train_data: 训练数据
            valid_data: 验证数据
            test_data: 测试数据
            text_dict: 文本字典
            
        Returns:
            统计信息字典
        """
        # 合并所有数据进行统计
        all_data = train_data + valid_data + test_data
        
        # 用户和物品统计
        users = set()
        items = set()
        user_interactions = defaultdict(int)
        item_interactions = defaultdict(int)
        
        for user_id, item_id in all_data:
            users.add(user_id)
            items.add(item_id)
            user_interactions[user_id] += 1
            item_interactions[item_id] += 1
        
        # 序列长度统计
        user_sequences = defaultdict(list)
        for user_id, item_id in train_data:
            user_sequences[user_id].append(item_id)
        
        sequence_lengths = [len(seq) for seq in user_sequences.values()]
        
        stats = {
            # 基本统计
            'total_users': len(users),
            'total_items': len(items),
            'total_interactions': len(all_data),
            
            # 数据分割统计
            'train_interactions': len(train_data),
            'valid_interactions': len(valid_data),
            'test_interactions': len(test_data),
            
            # 用户统计
            'min_user_id': min(users),
            'max_user_id': max(users),
            'avg_user_interactions': np.mean(list(user_interactions.values())),
            'std_user_interactions': np.std(list(user_interactions.values())),
            
            # 物品统计
            'min_item_id': min(items),
            'max_item_id': max(items),
            'avg_item_interactions': np.mean(list(item_interactions.values())),
            'std_item_interactions': np.std(list(item_interactions.values())),
            
            # 序列统计
            'avg_sequence_length': np.mean(sequence_lengths),
            'std_sequence_length': np.std(sequence_lengths),
            'min_sequence_length': min(sequence_lengths),
            'max_sequence_length': max(sequence_lengths),
            
            # 文本字典统计
            'has_text_dict': text_dict is not None,
            'text_dict_items': len(text_dict.get('title', {})) if text_dict else 0
        }
        
        return stats
    
    def generate_llm_srec_compatible_format(self) -> None:
        """
        生成与LLM-SRec兼容的数据格式
        
        创建以下文件：
        1. 用户序列数据（JSON格式）
        2. 物品元数据（JSON格式）
        3. 数据集统计信息
        4. 配置文件
        """
        logger.info("Generating LLM-SRec compatible format...")
        
        # 加载数据
        train_data = self._load_interaction_file(self.train_file)
        valid_data = self._load_interaction_file(self.valid_file)
        test_data = self._load_interaction_file(self.test_file)
        text_dict = self._load_text_dict()
        
        # 生成用户序列数据
        self._generate_user_sequences(train_data, valid_data, test_data)
        
        # 生成物品元数据
        if text_dict:
            self._generate_item_metadata(text_dict)
        
        # 保存统计信息
        self._save_statistics()
        
        # 生成配置文件
        self._generate_config_file()
        
        logger.info(f"LLM-SRec compatible format generated in {self.output_dir}")
    
    def _generate_user_sequences(self, train_data: List[Tuple[int, int]], 
                               valid_data: List[Tuple[int, int]], 
                               test_data: List[Tuple[int, int]]) -> None:
        """
        生成用户序列数据文件
        
        Args:
            train_data: 训练数据
            valid_data: 验证数据
            test_data: 测试数据
        """
        # 构建用户序列
        user_sequences = {
            'train': defaultdict(list),
            'valid': defaultdict(list),
            'test': defaultdict(list)
        }
        
        for user_id, item_id in train_data:
            user_sequences['train'][user_id].append(item_id)
        
        for user_id, item_id in valid_data:
            user_sequences['valid'][user_id].append(item_id)
        
        for user_id, item_id in test_data:
            user_sequences['test'][user_id].append(item_id)
        
        # 保存为JSON格式
        for split in ['train', 'valid', 'test']:
            output_file = os.path.join(self.output_dir, f'{split}_sequences.json')
            
            # 转换为普通字典（JSON序列化需要）
            sequences_dict = {str(k): v for k, v in user_sequences[split].items()}
            
            with open(output_file, 'w') as f:
                json.dump(sequences_dict, f, indent=2)
            
            logger.info(f"Saved {split} sequences to {output_file}")
    
    def _generate_item_metadata(self, text_dict: Dict) -> None:
        """
        生成物品元数据文件
        
        Args:
            text_dict: 物品文本字典
        """
        metadata_file = os.path.join(self.output_dir, 'item_metadata.json')
        
        # 重新组织元数据格式
        metadata = {}
        
        if 'title' in text_dict:
            for item_id, title in text_dict['title'].items():
                metadata[str(item_id)] = {
                    'title': title,
                    'description': text_dict.get('description', {}).get(item_id, ''),
                    'time': text_dict.get('time', {}).get(item_id, 0)
                }
        
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved item metadata to {metadata_file}")
    
    def _save_statistics(self) -> None:
        """保存数据集统计信息"""
        stats_file = os.path.join(self.output_dir, 'dataset_statistics.json')
        
        with open(stats_file, 'w') as f:
            json.dump(self.stats, f, indent=2)
        
        logger.info(f"Saved dataset statistics to {stats_file}")
    
    def _generate_config_file(self) -> None:
        """生成数据集配置文件"""
        config = {
            'dataset_name': 'Movies_and_TV',
            'data_source': 'LLM-SRec-master',
            'preprocessing_date': pd.Timestamp.now().isoformat(),
            'statistics': self.stats,
            'file_paths': {
                'train_sequences': 'train_sequences.json',
                'valid_sequences': 'valid_sequences.json',
                'test_sequences': 'test_sequences.json',
                'item_metadata': 'item_metadata.json',
                'statistics': 'dataset_statistics.json'
            },
            'compatibility': {
                'llm_srec_format': True,
                'novel_format': True,
                'original_format': True
            }
        }
        
        config_file = os.path.join(self.output_dir, 'dataset_config.json')
        
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"Saved dataset config to {config_file}")
    
    def validate_data_integrity(self) -> bool:
        """
        验证数据完整性
        
        Returns:
            数据是否完整有效
        """
        logger.info("Validating data integrity...")
        
        try:
            # 检查文件存在性
            required_files = [self.train_file, self.valid_file, self.test_file]
            for file_path in required_files:
                if not os.path.exists(file_path):
                    logger.error(f"Required file missing: {file_path}")
                    return False
            
            # 检查数据格式
            for file_path in required_files:
                with open(file_path, 'r') as f:
                    for i, line in enumerate(f):
                        if i >= 10:  # 只检查前10行
                            break
                        line = line.strip()
                        if line:
                            parts = line.split()
                            if len(parts) != 2:
                                logger.error(f"Invalid format in {file_path}, line {i+1}: {line}")
                                return False
                            try:
                                int(parts[0])  # user_id
                                int(parts[1])  # item_id
                            except ValueError:
                                logger.error(f"Non-integer values in {file_path}, line {i+1}: {line}")
                                return False
            
            logger.info("Data integrity validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Data integrity validation failed: {e}")
            return False

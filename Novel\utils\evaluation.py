"""
推荐系统评估模块

实现与LLM-SRec项目完全一致的评估指标和方法，
确保对比实验的公平性和可靠性。

支持的评估指标：
1. NDCG@K (Normalized Discounted Cumulative Gain)
2. Recall@K (召回率)
3. Precision@K (精确率)
4. Hit Rate@K (命中率)
5. MRR (Mean Reciprocal Rank)
"""

import numpy as np
import torch
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
from collections import defaultdict

logger = logging.getLogger(__name__)


class RecommendationEvaluator:
    """
    推荐系统评估器
    
    提供与LLM-SRec项目一致的评估指标计算方法，
    支持批量评估和详细的性能分析。
    """
    
    def __init__(self, k_values: List[int] = [5, 10, 20]):
        """
        初始化评估器
        
        Args:
            k_values: 评估的K值列表，默认为[5, 10, 20]
        """
        self.k_values = k_values
        self.reset_metrics()
    
    def reset_metrics(self):
        """重置评估指标"""
        self.metrics = {
            'ndcg': {k: [] for k in self.k_values},
            'recall': {k: [] for k in self.k_values},
            'precision': {k: [] for k in self.k_values},
            'hit_rate': {k: [] for k in self.k_values},
            'mrr': []
        }
    
    def evaluate_batch(self, 
                      predictions: Union[torch.Tensor, np.ndarray], 
                      targets: Union[torch.Tensor, np.ndarray, List[List[int]]],
                      user_ids: Optional[List[int]] = None) -> Dict[str, float]:
        """
        批量评估推荐结果
        
        Args:
            predictions: 预测结果，形状为 [batch_size, num_items] 或 [batch_size, k]
            targets: 真实标签，可以是物品ID列表或二进制矩阵
            user_ids: 用户ID列表（可选）
            
        Returns:
            评估指标字典
        """
        if isinstance(predictions, torch.Tensor):
            predictions = predictions.cpu().numpy()
        
        if isinstance(targets, torch.Tensor):
            targets = targets.cpu().numpy()
        
        batch_size = predictions.shape[0]
        
        for i in range(batch_size):
            pred = predictions[i]
            target = targets[i] if isinstance(targets, np.ndarray) else targets[i]
            
            # 计算单个样本的指标
            sample_metrics = self._evaluate_single_sample(pred, target)
            
            # 累积指标
            for metric_name, metric_values in sample_metrics.items():
                if metric_name == 'mrr':
                    self.metrics['mrr'].append(metric_values)
                else:
                    for k in self.k_values:
                        if k in metric_values:
                            self.metrics[metric_name][k].append(metric_values[k])
        
        return self.get_average_metrics()
    
    def _evaluate_single_sample(self, 
                               prediction: np.ndarray, 
                               target: Union[np.ndarray, List[int]]) -> Dict[str, Any]:
        """
        评估单个样本
        
        Args:
            prediction: 单个样本的预测分数
            target: 单个样本的真实标签
            
        Returns:
            单个样本的评估指标
        """
        # 获取Top-K推荐列表
        if len(prediction.shape) == 1:
            # 如果是分数向量，取Top-K
            max_k = max(self.k_values)
            top_k_indices = np.argsort(prediction)[::-1][:max_k]
        else:
            # 如果已经是排序后的索引
            top_k_indices = prediction
        
        # 处理目标标签
        if isinstance(target, list):
            target_items = set(target)
        elif isinstance(target, np.ndarray):
            if target.dtype == bool or np.all(np.isin(target, [0, 1])):
                # 二进制向量
                target_items = set(np.where(target == 1)[0])
            else:
                # 物品ID列表
                target_items = set(target)
        else:
            target_items = {target}
        
        # 计算各项指标
        metrics = {
            'ndcg': {},
            'recall': {},
            'precision': {},
            'hit_rate': {}
        }
        
        for k in self.k_values:
            if k <= len(top_k_indices):
                top_k = top_k_indices[:k]
                
                # 计算NDCG@K
                metrics['ndcg'][k] = self._calculate_ndcg_at_k(top_k, target_items, k)
                
                # 计算Recall@K
                metrics['recall'][k] = self._calculate_recall_at_k(top_k, target_items)
                
                # 计算Precision@K
                metrics['precision'][k] = self._calculate_precision_at_k(top_k, target_items)
                
                # 计算Hit Rate@K
                metrics['hit_rate'][k] = self._calculate_hit_rate_at_k(top_k, target_items)
        
        # 计算MRR
        metrics['mrr'] = self._calculate_mrr(top_k_indices, target_items)
        
        return metrics
    
    def _calculate_ndcg_at_k(self, top_k: np.ndarray, target_items: set, k: int) -> float:
        """
        计算NDCG@K
        
        Args:
            top_k: Top-K推荐列表
            target_items: 目标物品集合
            k: K值
            
        Returns:
            NDCG@K值
        """
        # 计算DCG
        dcg = 0.0
        for i, item in enumerate(top_k):
            if item in target_items:
                dcg += 1.0 / np.log2(i + 2)  # i+2 because log2(1) = 0
        
        # 计算IDCG
        idcg = 0.0
        num_relevant = min(len(target_items), k)
        for i in range(num_relevant):
            idcg += 1.0 / np.log2(i + 2)
        
        # 计算NDCG
        if idcg == 0:
            return 0.0
        return dcg / idcg
    
    def _calculate_recall_at_k(self, top_k: np.ndarray, target_items: set) -> float:
        """
        计算Recall@K
        
        Args:
            top_k: Top-K推荐列表
            target_items: 目标物品集合
            
        Returns:
            Recall@K值
        """
        if len(target_items) == 0:
            return 0.0
        
        hits = len(set(top_k) & target_items)
        return hits / len(target_items)
    
    def _calculate_precision_at_k(self, top_k: np.ndarray, target_items: set) -> float:
        """
        计算Precision@K
        
        Args:
            top_k: Top-K推荐列表
            target_items: 目标物品集合
            
        Returns:
            Precision@K值
        """
        if len(top_k) == 0:
            return 0.0
        
        hits = len(set(top_k) & target_items)
        return hits / len(top_k)
    
    def _calculate_hit_rate_at_k(self, top_k: np.ndarray, target_items: set) -> float:
        """
        计算Hit Rate@K
        
        Args:
            top_k: Top-K推荐列表
            target_items: 目标物品集合
            
        Returns:
            Hit Rate@K值（0或1）
        """
        hits = len(set(top_k) & target_items)
        return 1.0 if hits > 0 else 0.0
    
    def _calculate_mrr(self, ranked_list: np.ndarray, target_items: set) -> float:
        """
        计算MRR (Mean Reciprocal Rank)
        
        Args:
            ranked_list: 排序后的推荐列表
            target_items: 目标物品集合
            
        Returns:
            MRR值
        """
        for i, item in enumerate(ranked_list):
            if item in target_items:
                return 1.0 / (i + 1)
        return 0.0
    
    def get_average_metrics(self) -> Dict[str, float]:
        """
        获取平均评估指标
        
        Returns:
            平均评估指标字典
        """
        avg_metrics = {}
        
        # 计算各K值的平均指标
        for metric_name in ['ndcg', 'recall', 'precision', 'hit_rate']:
            for k in self.k_values:
                if self.metrics[metric_name][k]:
                    avg_value = np.mean(self.metrics[metric_name][k])
                    avg_metrics[f'{metric_name}@{k}'] = avg_value
        
        # 计算平均MRR
        if self.metrics['mrr']:
            avg_metrics['mrr'] = np.mean(self.metrics['mrr'])
        
        return avg_metrics
    
    def get_detailed_metrics(self) -> Dict[str, Any]:
        """
        获取详细评估指标（包括标准差等）
        
        Returns:
            详细评估指标字典
        """
        detailed_metrics = {}
        
        for metric_name in ['ndcg', 'recall', 'precision', 'hit_rate']:
            for k in self.k_values:
                if self.metrics[metric_name][k]:
                    values = self.metrics[metric_name][k]
                    detailed_metrics[f'{metric_name}@{k}'] = {
                        'mean': np.mean(values),
                        'std': np.std(values),
                        'min': np.min(values),
                        'max': np.max(values),
                        'count': len(values)
                    }
        
        # MRR详细指标
        if self.metrics['mrr']:
            values = self.metrics['mrr']
            detailed_metrics['mrr'] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values),
                'count': len(values)
            }
        
        return detailed_metrics
    
    def print_metrics(self, detailed: bool = False):
        """
        打印评估指标
        
        Args:
            detailed: 是否打印详细指标
        """
        if detailed:
            metrics = self.get_detailed_metrics()
            print("\n=== 详细评估指标 ===")
            for metric_name, metric_data in metrics.items():
                print(f"{metric_name}:")
                print(f"  Mean: {metric_data['mean']:.4f}")
                print(f"  Std:  {metric_data['std']:.4f}")
                print(f"  Min:  {metric_data['min']:.4f}")
                print(f"  Max:  {metric_data['max']:.4f}")
                print(f"  Count: {metric_data['count']}")
        else:
            metrics = self.get_average_metrics()
            print("\n=== 平均评估指标 ===")
            for metric_name, value in metrics.items():
                print(f"{metric_name}: {value:.4f}")


def compare_with_llm_srec(novel_metrics: Dict[str, float], 
                         llm_srec_metrics: Dict[str, float]) -> Dict[str, Any]:
    """
    与LLM-SRec结果进行对比
    
    Args:
        novel_metrics: Novel项目的评估指标
        llm_srec_metrics: LLM-SRec项目的评估指标
        
    Returns:
        对比结果字典
    """
    comparison = {
        'novel_metrics': novel_metrics,
        'llm_srec_metrics': llm_srec_metrics,
        'improvements': {},
        'summary': {}
    }
    
    # 计算改进幅度
    for metric_name in novel_metrics:
        if metric_name in llm_srec_metrics:
            novel_value = novel_metrics[metric_name]
            llm_srec_value = llm_srec_metrics[metric_name]
            
            if llm_srec_value != 0:
                improvement = (novel_value - llm_srec_value) / llm_srec_value * 100
                comparison['improvements'][metric_name] = improvement
    
    # 生成总结
    if comparison['improvements']:
        avg_improvement = np.mean(list(comparison['improvements'].values()))
        comparison['summary'] = {
            'average_improvement': avg_improvement,
            'best_metric': max(comparison['improvements'], key=comparison['improvements'].get),
            'worst_metric': min(comparison['improvements'], key=comparison['improvements'].get)
        }
    
    return comparison

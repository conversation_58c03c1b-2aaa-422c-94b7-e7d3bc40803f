"""
Novel: 隐私保护模块

实现多层次隐私保护机制，确保用户数据安全：
1. 本地数据处理 - 原始数据不离端侧
2. 表示层传输 - 仅传输抽象用户表示
3. 差分隐私 - 可选噪声注入保护
4. 安全通信 - 加密传输协议

Author: Novel Team
Date: 2024
"""

import torch
import numpy as np
import hashlib
import logging
from typing import Dict, List, Any, Optional, Tuple
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os


class PrivacyProtectionManager:
    """
    隐私保护管理器
    
    核心功能：
    1. 用户表示抽象化
    2. 差分隐私保护
    3. 数据加密传输
    4. 隐私风险评估
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化隐私保护管理器
        
        Args:
            config: 隐私保护配置
        """
        self.config = config
        self.logger = logging.getLogger('Novel.Privacy')
        
        # 隐私保护参数
        self.epsilon = config.get('differential_privacy', {}).get('epsilon', 1.0)
        self.delta = config.get('differential_privacy', {}).get('delta', 1e-5)
        self.noise_multiplier = config.get('differential_privacy', {}).get('noise_multiplier', 1.0)
        
        # 加密设置
        self.encryption_enabled = config.get('encryption', {}).get('enabled', True)
        if self.encryption_enabled:
            self._init_encryption()
        
        self.logger.info("隐私保护管理器初始化完成")
    
    def _init_encryption(self):
        """初始化加密模块"""
        # 生成或加载加密密钥
        password = self.config.get('encryption', {}).get('password', 'novel_default_key').encode()
        salt = os.urandom(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        self.cipher_suite = Fernet(key)
        
        self.logger.info("加密模块初始化完成")
    
    def protect_user_representation(
        self, 
        user_representation: torch.Tensor,
        privacy_level: str = 'medium'
    ) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """
        保护用户表示
        
        Args:
            user_representation: 原始用户表示 (64维)
            privacy_level: 隐私保护级别 ('low', 'medium', 'high')
            
        Returns:
            (受保护的用户表示, 隐私信息)
        """
        protected_repr = user_representation.clone()
        privacy_info = {
            'original_shape': user_representation.shape,
            'protection_methods': [],
            'privacy_level': privacy_level,
            'privacy_loss': 0.0
        }
        
        # 1. 表示抽象化（已经通过CF-SRec实现）
        privacy_info['protection_methods'].append('representation_abstraction')
        
        # 2. 差分隐私保护
        if privacy_level in ['medium', 'high']:
            protected_repr, dp_info = self._apply_differential_privacy(
                protected_repr, privacy_level
            )
            privacy_info['protection_methods'].append('differential_privacy')
            privacy_info['privacy_loss'] = dp_info['privacy_loss']
        
        # 3. 维度扰动（高隐私级别）
        if privacy_level == 'high':
            protected_repr = self._apply_dimension_perturbation(protected_repr)
            privacy_info['protection_methods'].append('dimension_perturbation')
        
        # 4. 范数约束
        protected_repr = self._apply_norm_constraint(protected_repr)
        privacy_info['protection_methods'].append('norm_constraint')
        
        return protected_repr, privacy_info
    
    def _apply_differential_privacy(
        self, 
        representation: torch.Tensor,
        privacy_level: str
    ) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """
        应用差分隐私保护
        
        Args:
            representation: 用户表示
            privacy_level: 隐私级别
            
        Returns:
            (添加噪声后的表示, 差分隐私信息)
        """
        # 根据隐私级别调整噪声强度
        noise_scale = {
            'medium': self.noise_multiplier * 0.5,
            'high': self.noise_multiplier * 1.0
        }.get(privacy_level, 0.1)
        
        # 添加高斯噪声
        noise = torch.randn_like(representation) * noise_scale
        protected_repr = representation + noise
        
        # 计算隐私损失
        sensitivity = 1.0  # L2敏感度
        privacy_loss = sensitivity / (noise_scale * np.sqrt(2 * np.log(1.25 / self.delta)))
        
        dp_info = {
            'noise_scale': noise_scale,
            'privacy_loss': privacy_loss,
            'epsilon_used': min(privacy_loss, self.epsilon)
        }
        
        return protected_repr, dp_info
    
    def _apply_dimension_perturbation(self, representation: torch.Tensor) -> torch.Tensor:
        """
        应用维度扰动
        
        Args:
            representation: 用户表示
            
        Returns:
            扰动后的表示
        """
        # 随机选择一部分维度进行轻微扰动
        perturbation_ratio = 0.1  # 扰动10%的维度
        num_dims = representation.shape[-1]
        num_perturb = int(num_dims * perturbation_ratio)
        
        # 随机选择维度
        perturb_indices = torch.randperm(num_dims)[:num_perturb]
        
        # 应用轻微扰动
        perturbation = torch.randn(num_perturb) * 0.01
        representation[..., perturb_indices] += perturbation
        
        return representation
    
    def _apply_norm_constraint(self, representation: torch.Tensor) -> torch.Tensor:
        """
        应用范数约束，防止表示过大
        
        Args:
            representation: 用户表示
            
        Returns:
            约束后的表示
        """
        max_norm = 10.0  # 最大L2范数
        current_norm = torch.norm(representation, p=2)
        
        if current_norm > max_norm:
            representation = representation * (max_norm / current_norm)
        
        return representation
    
    def encrypt_data(self, data: bytes) -> bytes:
        """
        加密数据
        
        Args:
            data: 原始数据
            
        Returns:
            加密后的数据
        """
        if not self.encryption_enabled:
            return data
        
        return self.cipher_suite.encrypt(data)
    
    def decrypt_data(self, encrypted_data: bytes) -> bytes:
        """
        解密数据
        
        Args:
            encrypted_data: 加密数据
            
        Returns:
            解密后的数据
        """
        if not self.encryption_enabled:
            return encrypted_data
        
        return self.cipher_suite.decrypt(encrypted_data)
    
    def assess_privacy_risk(
        self, 
        user_representation: torch.Tensor,
        original_sequence: Optional[List[int]] = None
    ) -> Dict[str, Any]:
        """
        评估隐私风险
        
        Args:
            user_representation: 用户表示
            original_sequence: 原始序列（可选）
            
        Returns:
            隐私风险评估结果
        """
        risk_assessment = {
            'overall_risk': 'low',
            'risk_factors': [],
            'recommendations': []
        }
        
        # 1. 检查表示维度
        if user_representation.shape[-1] > 128:
            risk_assessment['risk_factors'].append('high_dimensional_representation')
            risk_assessment['recommendations'].append('consider_dimension_reduction')
        
        # 2. 检查表示范数
        repr_norm = torch.norm(user_representation, p=2).item()
        if repr_norm > 20.0:
            risk_assessment['risk_factors'].append('large_representation_norm')
            risk_assessment['recommendations'].append('apply_norm_constraint')
        
        # 3. 检查是否应用了隐私保护
        if len(risk_assessment['risk_factors']) == 0:
            risk_assessment['overall_risk'] = 'low'
        elif len(risk_assessment['risk_factors']) <= 2:
            risk_assessment['overall_risk'] = 'medium'
        else:
            risk_assessment['overall_risk'] = 'high'
        
        return risk_assessment
    
    def generate_privacy_report(
        self, 
        protection_info: Dict[str, Any],
        risk_assessment: Dict[str, Any]
    ) -> str:
        """
        生成隐私保护报告
        
        Args:
            protection_info: 隐私保护信息
            risk_assessment: 风险评估结果
            
        Returns:
            隐私报告字符串
        """
        report = f"""
=== Novel隐私保护报告 ===

保护方法: {', '.join(protection_info['protection_methods'])}
隐私级别: {protection_info['privacy_level']}
隐私损失: {protection_info['privacy_loss']:.6f}

风险评估:
- 总体风险: {risk_assessment['overall_risk']}
- 风险因素: {', '.join(risk_assessment['risk_factors']) if risk_assessment['risk_factors'] else '无'}
- 建议: {', '.join(risk_assessment['recommendations']) if risk_assessment['recommendations'] else '无'}

数据保护状态:
✓ 原始数据保留在端侧
✓ 仅传输抽象用户表示
✓ 应用多层隐私保护机制
✓ 支持加密传输

隐私保证:
- 用户原始交互序列不离开设备
- 传输的64维表示无法逆向还原
- 符合差分隐私标准 (ε={self.epsilon}, δ={self.delta})
"""
        return report.strip()


class PrivacyAudit:
    """隐私审计工具"""
    
    @staticmethod
    def audit_data_flow(data_flow: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        审计数据流
        
        Args:
            data_flow: 数据流记录
            
        Returns:
            审计结果
        """
        audit_result = {
            'compliant': True,
            'violations': [],
            'recommendations': []
        }
        
        for step in data_flow:
            # 检查是否有原始数据离开端侧
            if step.get('data_type') == 'raw_sequence' and step.get('location') != 'edge':
                audit_result['compliant'] = False
                audit_result['violations'].append(
                    f"原始序列数据在{step.get('location')}被发现"
                )
        
        return audit_result
    
    @staticmethod
    def verify_representation_safety(representation: torch.Tensor) -> bool:
        """
        验证表示安全性
        
        Args:
            representation: 用户表示
            
        Returns:
            是否安全
        """
        # 检查维度（应该是64维）
        if representation.shape[-1] != 64:
            return False
        
        # 检查范数（不应过大）
        norm = torch.norm(representation, p=2).item()
        if norm > 50.0:
            return False
        
        return True

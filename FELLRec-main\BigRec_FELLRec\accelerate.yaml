compute_environment: LOCAL_MACHINE
# deepspeed_config:
#   gradient_accumulation_steps: 8
#   gradient_clipping: 1.0
#   offload_optimizer_device: none
#   offload_param_device: none
#   zero3_init_flag: false
#   zero_stage: 2
distributed_type: MULTI_GPU
downcast_bf16: 'no'
machine_rank: 0
main_training_function: main
mixed_precision: fp16
num_machines: 1
num_processes: 4
gpu_ids: 0,1,2,3
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
main_process_port: 29503

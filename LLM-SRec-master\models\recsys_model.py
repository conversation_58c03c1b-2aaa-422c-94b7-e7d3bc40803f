"""
推荐系统模型封装（models/recsys_model.py）

功能概述：
- 从本地目录加载已预训练好的序列推荐模型（当前为 SASRec）检查点
- 构建对应的 PyTorch 模型并加载参数
- 将其参数全部冻结，用作特征提取器/教师模型，为上层 LLM-SRec 提供用户/物品的表示

使用场景：
- 训练或推理 LLM-SRec 时，通过 RecSys 包装器快速获得预训练的 CF-SRec（SASRec）模型

目录约定：
- 预训练权重默认保存在 ./SeqRec/{recsys}/{pre_trained}/ 目录
  其中会包含一个以 .pth 结尾的模型文件（仅允许存在一个）

主要组件：
- load_checkpoint(): 从磁盘加载预训练模型的检查点文件
- RecSys 类: 封装预训练模型的包装器，提供统一接口

使用流程示例：
1. 确保预训练模型已保存在正确目录：./SeqRec/sasrec/Industrial_and_Scientific/model.pth
2. 创建 RecSys 实例：recsys = RecSys('sasrec', 'Industrial_and_Scientific', 'cuda:0')
3. 获取用户表示：user_repr = recsys.model(user_ids, seq, pos, neg, mode='log_only')
4. 获取物品嵌入：item_emb = recsys.model.item_emb(item_ids)

注意事项：
- 模型参数已冻结，不会在训练中更新
- 支持不同设备间的模型迁移（CPU ↔ GPU）
- 检查点文件必须包含完整的模型初始化参数和权重
"""

import contextlib
import logging
import os
import glob

import torch
import torch.nn as nn
import torch.distributed as dist
import torch.nn.functional as F

from utils import *
from SeqRec.sasrec.model import SASRec
# from Seq_Exp.SeqRec.sasrec.model import SASRec


def load_checkpoint(recsys, pre_trained):
    """
    从磁盘加载预训练的序列推荐模型检查点

    Args:
        recsys (str): 模型名称（例如 'sasrec'），对应子目录名
        pre_trained (str): 预训练数据集标识（例如 'Industrial_and_Scientific'），对应子目录名

    Returns:
        Tuple[dict, OrderedDict]:
            - kwargs: 初始化底层模型(SASRec)所需的关键参数字典
                     包含 {'user_num': int, 'item_num': int, 'args': Namespace}
                     其中 args 包含模型超参数如 hidden_units, maxlen, dropout_rate 等
            - checkpoint: 通过 state_dict 保存的模型权重张量字典
                         包含所有可学习参数的名称到张量的映射

    目录结构示例：
        ./SeqRec/{recsys}/{pre_trained}/xxx.pth

    注意：该目录下必须且只能存在一个 .pth 文件
    """
    # 构建预训练模型检查点的目录路径
    # 例如: './SeqRec/sasrec/Industrial_and_Scientific/'
    path = f'./SeqRec/{recsys}/{pre_trained}/'

    # 使用 utils 中的 find_filepath 函数查找指定目录下所有 .pth 文件
    # find_filepath 工作原理：
    # 1. 遍历 target_path 目录下的所有文件
    # 2. 检查文件名是否包含 target_word（这里是 '.pth'）
    # 3. 返回匹配文件的完整路径列表（格式：target_path + filename）
    # 例如：find_filepath('./SeqRec/sasrec/Industrial/', '.pth')
    #      可能返回 ['./SeqRec/sasrec/Industrial/model_epoch_200.pth']
    pth_file_path = find_filepath(path, '.pth')

    # 确保目录中只有一个 .pth 文件，避免加载错误的模型
    # 如果有多个文件，用户需要手动清理多余的模型文件
    assert len(pth_file_path) == 1, 'There are more than two models in this dir. You need to remove other model files.\n'

    # 使用 torch.load 加载检查点文件
    # 参数说明：
    # - pth_file_path[0]: 检查点文件的完整路径
    # - map_location="cpu": 将模型加载到CPU内存，避免GPU内存不足或设备不匹配问题
    # - weights_only=False: 允许加载包含Python对象的pickle文件（如args参数）
    #   注意：设为False存在安全风险，但这里需要加载模型初始化参数
    kwargs, checkpoint = torch.load(pth_file_path[0], map_location="cpu", weights_only=False)

    # 记录加载信息到日志，便于调试和追踪
    logging.info("load checkpoint from %s" % pth_file_path[0])

    # 返回模型初始化参数和权重字典
    # kwargs: 用于重新创建相同结构的模型实例
    # checkpoint: 用于恢复模型的训练状态
    return kwargs, checkpoint

class RecSys(nn.Module):
    """
    RecSys 封装类 - 预训练序列推荐模型的包装器

    作用：
    - 根据给定的模型名与预训练数据标识，从磁盘加载预训练的SASRec模型
    - 冻结其参数（不参与训练），并提供给上层作为"教师模型/特征抽取器"
    - 在 LLM-SRec 框架中，用于提供用户历史行为序列的隐藏表示

    常用属性：
    - self.model (SASRec): 已加载并冻结权重的 SASRec 实例，可调用其方法获取表示
    - self.item_num (int): 物品总数（来自SASRec，包含padding的0号位置）
    - self.user_num (int): 用户总数（来自SASRec）
    - self.hidden_units (int): 隐藏维度（SASRec的嵌入维度，也是输出表示的维度）

    使用示例：
        recsys = RecSys('sasrec', 'Industrial_and_Scientific', 'cuda:0')
        # 获取用户序列的最后一个位置表示（用于知识蒸馏）
        user_repr = recsys.model(user_ids, seq, pos, neg, mode='log_only')
        # 获取物品嵌入
        item_emb = recsys.model.item_emb(item_ids)
    """

    def __init__(self, recsys_model, pre_trained_data, device):
        """
        初始化 RecSys 包装器

        Args:
            recsys_model (str): 推荐系统模型名称，如 'sasrec'
            pre_trained_data (str): 预训练数据集名称，如 'Industrial_and_Scientific'
            device (str): 目标计算设备，如 'cuda:0' 或 'cpu'
        """
        super().__init__()

        # 步骤1: 从磁盘加载预训练模型的检查点
        # kwargs 包含模型初始化所需的参数（user_num, item_num, args等）
        # checkpoint 包含训练好的模型权重（state_dict格式）
        kwargs, checkpoint = load_checkpoint(recsys_model, pre_trained_data)

        # 步骤2: 更新设备配置
        # 将目标设备信息写入args中，确保模型内部的张量操作在正确设备上进行
        # 这对于SASRec内部的嵌入层和计算操作很重要
        kwargs['args'].device = device

        # 步骤3: 重新创建SASRec模型实例
        # 使用保存的初始化参数重建与训练时完全相同的模型结构
        # **kwargs 解包传递 user_num, item_num, args 等参数
        model = SASRec(**kwargs)

        # 步骤4: 加载预训练权重
        # 将检查点中保存的权重加载到新创建的模型中
        # 这会恢复所有嵌入层、注意力层、前馈网络等的训练状态
        model.load_state_dict(checkpoint)

        # 步骤5: 冻结所有模型参数
        # 设置 requires_grad=False 防止这些参数在后续训练中被更新
        # 冻结的意义：
        # 1. 保持预训练模型的知识不被破坏
        # 2. 减少计算开销（不需要计算这些参数的梯度）
        # 3. 在LLM-SRec中作为固定的特征提取器使用
        for p in model.parameters():
            p.requires_grad = False

        # 步骤6: 提取并保存关键模型信息
        # 这些属性会被上层模块（如llmrec_model）频繁使用

        # 物品总数：包含真实物品 + padding位置(0)
        # 数据类型：int，用于随机采样负样本、构建候选集等
        self.item_num = model.item_num

        # 用户总数：数据集中的用户数量
        # 数据类型：int，主要用于数据验证和统计
        self.user_num = model.user_num

        # 步骤7: 将模型移动到目标设备并保存引用
        # 确保模型的所有参数和缓冲区都在指定设备上
        # 这是实际进行推理时使用的模型实例
        self.model = model.to(device)

        # 隐藏单元数（嵌入维度）：SASRec的表示维度
        # 数据类型：int，用于上层模块设计投影层的输入维度
        # 例如：item_emb_proj 需要知道这个维度来设计线性层
        self.hidden_units = kwargs['args'].hidden_units

    def forward(self, *args, **kwargs):
        """
        前向传播方法 - 友好提示用法

        注意：该包装类本身不定义新的前向逻辑。
        如需获取表示，请直接调用 self.model 的相应方法：

        常用调用方式：
        - 获取用户序列表示: self.model(user_ids, seq, pos, neg, mode='log_only')
        - 获取物品嵌入: self.model.item_emb(item_ids) 或 self.model.item_emb[item_ids]
        - 完整前向传播: self.model(user_ids, seq, pos, neg)

        这里保留一个占位的 forward 以避免误用，并提供使用指导。
        """
        print('RecSys wrapper: please call self.model (SASRec) directly.')
        print('Example: user_repr = recsys.model(u, seq, pos, neg, mode="log_only")')
        print('Example: item_emb = recsys.model.item_emb(item_ids)')
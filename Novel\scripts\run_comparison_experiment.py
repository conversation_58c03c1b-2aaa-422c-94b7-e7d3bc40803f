#!/usr/bin/env python3
"""
Novel vs LLM-SRec 对比实验运行脚本

用于运行Novel项目与LLM-SRec项目的对比实验，确保实验设置的一致性。

使用方法：
    python scripts/run_comparison_experiment.py --config experiments/configs/movies_tv_comparison.yaml

功能：
1. 加载实验配置
2. 运行Novel模型实验
3. 加载LLM-SRec基准结果
4. 生成对比报告
5. 保存实验结果
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from experiments.comparison_framework import ComparisonFramework, ExperimentConfig, load_config_from_yaml

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('comparison_experiment.log')
    ]
)

logger = logging.getLogger(__name__)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Novel vs LLM-SRec 对比实验')
    
    parser.add_argument(
        '--config', 
        type=str, 
        required=True,
        help='实验配置文件路径 (YAML格式)'
    )
    
    parser.add_argument(
        '--llm_srec_results', 
        type=str, 
        default='./data/Movies_and_TV/Results.txt',
        help='LLM-SRec结果文件路径'
    )
    
    parser.add_argument(
        '--model_checkpoint', 
        type=str, 
        help='Novel模型检查点路径'
    )
    
    parser.add_argument(
        '--output_dir', 
        type=str, 
        help='实验结果输出目录（覆盖配置文件中的设置）'
    )
    
    parser.add_argument(
        '--dry_run', 
        action='store_true',
        help='仅验证配置和数据，不运行实际实验'
    )
    
    args = parser.parse_args()
    
    try:
        # 加载实验配置
        logger.info(f"加载实验配置: {args.config}")
        config = load_config_from_yaml(args.config)
        
        # 覆盖命令行参数
        if args.output_dir:
            config.output_dir = args.output_dir
        
        logger.info(f"实验配置加载完成: {config.experiment_name}")
        
        # 初始化对比框架
        framework = ComparisonFramework(config)
        
        # 加载数据
        framework.load_data()
        
        if args.dry_run:
            logger.info("干运行模式，实验验证完成")
            return 0
        
        # 初始化评估器
        framework.initialize_evaluator()
        
        # 加载Novel模型（这里需要根据实际模型实现）
        novel_model = load_novel_model(args.model_checkpoint, config)
        
        # 运行完整对比实验
        framework.run_complete_comparison(novel_model, args.llm_srec_results)
        
        logger.info("✅ 对比实验成功完成！")
        logger.info(f"结果保存在: {config.output_dir}")
        
        return 0
        
    except Exception as e:
        logger.error(f"实验运行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


def load_novel_model(checkpoint_path: str, config: ExperimentConfig):
    """
    加载Novel模型
    
    Args:
        checkpoint_path: 模型检查点路径
        config: 实验配置
        
    Returns:
        加载的模型实例
    """
    # 这里需要根据实际的Novel模型实现来加载模型
    # 暂时返回一个模拟模型
    
    logger.info(f"加载Novel模型: {checkpoint_path}")
    
    class MockNovelModel:
        """模拟Novel模型（用于测试）"""
        
        def __init__(self, config):
            self.config = config
        
        def eval(self):
            """设置为评估模式"""
            pass
        
        def predict(self, batch):
            """模拟预测"""
            import torch
            batch_size = batch['user_ids'].size(0)
            num_items = 1000  # 假设有1000个物品
            
            # 返回随机预测分数
            return torch.randn(batch_size, num_items)
    
    if checkpoint_path and os.path.exists(checkpoint_path):
        # 实际加载模型的代码
        logger.warning("实际模型加载功能尚未实现，使用模拟模型")
    
    return MockNovelModel(config)


def create_default_config():
    """创建默认实验配置"""
    config_content = """
# Novel vs LLM-SRec 对比实验配置

experiment_name: "Novel_vs_LLM-SRec_Movies_and_TV"
dataset_name: "Movies_and_TV"

# 数据配置
data_dir: "./data"
max_sequence_length: 128
batch_size: 32

# 评估配置
k_values: [5, 10, 20]
metrics: ["ndcg", "recall", "precision", "hit_rate", "mrr"]

# 模型配置
device: "cuda"
seed: 42

# 输出配置
output_dir: "./experiments/results/movies_tv_comparison"
save_predictions: true
save_detailed_metrics: true
"""
    
    # 创建配置目录
    config_dir = project_root / "experiments" / "configs"
    config_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存默认配置
    config_file = config_dir / "movies_tv_comparison.yaml"
    with open(config_file, 'w') as f:
        f.write(config_content)
    
    logger.info(f"默认配置已创建: {config_file}")
    return str(config_file)


if __name__ == "__main__":
    # 如果没有配置文件，创建默认配置
    if len(sys.argv) == 1:
        print("未提供配置文件，创建默认配置...")
        default_config = create_default_config()
        print(f"默认配置已创建: {default_config}")
        print(f"使用方法: python {sys.argv[0]} --config {default_config}")
        sys.exit(0)
    
    exit_code = main()
    sys.exit(exit_code)

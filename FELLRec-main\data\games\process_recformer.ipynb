{"cells": [{"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-07-27T02:02:57.048559Z", "start_time": "2025-07-27T02:02:56.516067Z"}}, "source": ["import os\n", "import json\n", "import gzip\n", "import numpy as np\n", "import pandas as pd \n", "import matplotlib.pyplot as plt\n", "import random\n", "from collections import defaultdict\n", "from tqdm import tqdm\n", "random.seed(2024)"], "outputs": [], "execution_count": 1}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# get the item id to title map\n", "save_path = './'\n", "asin_to_feature_map = {}\n", "with gzip.open('./raw_data/meta_Video_Games.json.gz', 'r') as file:\n", "    for line in file:\n", "        data = json.loads(line)\n", "        if \"asin\" in data:\n", "            asin_to_feature_map[data['asin']] = {}\n", "            asin_to_feature_map[data['asin']]['title'] = data['title']\n", "            asin_to_feature_map[data['asin']]['brand'] = data['brand']\n", "            category = ' '.join(data['category'])\n", "            asin_to_feature_map[data['asin']]['category'] = category\n", "\n", "meta_file = './meta_data.json'\n", "meta_f = open(meta_file, 'w', encoding='utf8')\n", "json.dump(asin_to_feature_map, meta_f)\n", "meta_f.close() \n"]}], "metadata": {"kernelspec": {"display_name": "torch-1.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 2}
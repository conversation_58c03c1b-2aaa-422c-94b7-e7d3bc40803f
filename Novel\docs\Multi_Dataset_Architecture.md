# Novel项目多数据集架构设计

## 🎯 设计目标

重新设计Novel项目的数据处理架构，使其能够像LLM-SRec项目一样支持多个数据集的便捷切换和复现，专注于实现Novel的核心架构：端云协同的大小模型推荐系统。

## 🏗️ 架构概览

### 核心设计原则

1. **统一接口**: 所有数据集使用相同的加载和处理接口
2. **配置驱动**: 通过配置文件和参数轻松切换数据集
3. **LLM-SRec兼容**: 保持与LLM-SRec项目的数据格式完全兼容
4. **易于扩展**: 支持新数据集的快速添加
5. **模块化设计**: 清晰的模块分离和职责划分

### 架构组件

```
Novel/
├── utils/
│   ├── data_utils.py           # 通用数据处理工具
│   ├── config_manager.py       # 配置管理器
│   └── movies_tv_preprocessor.py # 通用预处理器
├── config/
│   └── collaborative_config.yaml # 多数据集配置文件
├── scripts/
│   ├── preprocess_amazon_dataset.py # 通用预处理脚本
│   └── train_novel.py         # 通用训练脚本
└── examples/
    └── dataset_switching_demo.py # 数据集切换演示
```

## 📊 支持的数据集

### 当前支持的Amazon数据集

| 数据集名称 | 描述 | 文本特征 | 预期规模 |
|-----------|------|----------|----------|
| Movies_and_TV | 电影和电视节目推荐 | title, description | 11K用户, 17K物品 |
| Industrial_and_Scientific | 工业和科学用品推荐 | title, description, brand | 15K用户, 20K物品 |
| Sports_and_Outdoors | 体育和户外用品推荐 | title, description, brand, category | 25K用户, 30K物品 |
| Beauty_and_Personal_Care | 美容和个人护理推荐 | title, description, brand | 20K用户, 25K物品 |

### 数据集注册表机制

```python
# 数据集自动注册和发现
from utils.data_utils import DatasetRegistry, list_available_datasets

# 列出所有支持的数据集
datasets = list_available_datasets()
print(datasets)  # ['Movies_and_TV', 'Industrial_and_Scientific', ...]

# 获取数据集配置信息
config = DatasetRegistry.get_dataset_config('Movies_and_TV')
print(config['description'])  # '电影和电视节目推荐数据集'
```

## 🔧 使用方法

### 1. 数据集切换

#### 通过配置文件切换
```yaml
# config/collaborative_config.yaml
data:
  dataset: "Industrial_and_Scientific"  # 修改这里即可切换数据集
  data_dir: "./data"
  max_sequence_length: 128
```

#### 通过命令行参数切换
```bash
# 训练Movies_and_TV数据集
python scripts/train_novel.py --dataset Movies_and_TV --device 0 --train

# 训练Industrial_and_Scientific数据集
python scripts/train_novel.py --dataset Industrial_and_Scientific --device 0 --train

# 预处理Sports_and_Outdoors数据集
python scripts/preprocess_amazon_dataset.py --dataset Sports_and_Outdoors --data_dir ./data/Sports_and_Outdoors
```

#### 通过代码切换
```python
from utils.data_utils import create_data_loader
from utils.config_manager import ConfigManager

# 方法1: 直接创建数据加载器
config = {'dataset': 'Movies_and_TV', 'data_dir': './data'}
data_loader = create_data_loader('Movies_and_TV', config)

# 方法2: 使用配置管理器
config_manager = ConfigManager('config/collaborative_config.yaml')
config_manager.set_dataset('Industrial_and_Scientific')
```

### 2. 统一的数据处理接口

```python
# 所有数据集使用相同的接口
def process_any_dataset(dataset_name: str):
    # 创建数据加载器
    data_loader = create_data_loader(dataset_name, config)
    
    # 获取统计信息
    stats = data_loader.get_dataset_stats()
    
    # 获取数据加载器
    train_loader = data_loader.get_train_loader(batch_size=32)
    val_loader = data_loader.get_val_loader(batch_size=32)
    test_loader = data_loader.get_test_loader(batch_size=32)
    
    return train_loader, val_loader, test_loader

# 处理不同数据集
movies_loaders = process_any_dataset('Movies_and_TV')
industrial_loaders = process_any_dataset('Industrial_and_Scientific')
```

### 3. 数据预处理

```bash
# 列出支持的数据集
python scripts/preprocess_amazon_dataset.py --list_datasets

# 预处理指定数据集
python scripts/preprocess_amazon_dataset.py \
    --dataset Movies_and_TV \
    --data_dir ./data/Movies_and_TV \
    --generate_stats \
    --llm_srec_format

# 自动检测数据集类型
python scripts/preprocess_amazon_dataset.py \
    --data_dir ./data/Movies_and_TV \
    --auto_detect \
    --validate_only
```

## 🔄 与LLM-SRec的兼容性

### 数据格式兼容性

1. **文件命名**: 保持LLM-SRec的命名约定
   ```
   {dataset_name}_train.txt
   {dataset_name}_valid.txt
   {dataset_name}_test.txt
   text_name_dict.json.gz
   ```

2. **数据格式**: 保持相同的数据格式
   ```
   # 交互数据格式
   user_id item_id
   1 1
   1 2
   
   # 文本字典格式
   {
       'title': {item_id: title_text, ...},
       'description': {item_id: description_text, ...}
   }
   ```

3. **参数兼容**: 支持LLM-SRec风格的命令行参数
   ```bash
   # LLM-SRec风格
   python main.py --rec_pre_trained_data Movies_and_TV --device 0 --train
   
   # Novel风格（兼容）
   python scripts/train_novel.py --dataset Movies_and_TV --device 0 --train
   ```

## 🚀 快速开始

### 1. 环境准备
```bash
cd Novel
pip install torch numpy pandas pyyaml tqdm
```

### 2. 数据准备
```bash
# 确保数据集已下载到正确位置
ls data/Movies_and_TV/
# Movies_and_TV_train.txt  Movies_and_TV_valid.txt  Movies_and_TV_test.txt  text_name_dict.json.gz
```

### 3. 运行演示
```bash
# 运行多数据集架构演示
python examples/dataset_switching_demo.py

# 预处理数据集
python scripts/preprocess_amazon_dataset.py --dataset Movies_and_TV --data_dir ./data/Movies_and_TV --validate_only

# 训练模型（模拟）
python scripts/train_novel.py --dataset Movies_and_TV --device 0 --train
```

## 🔧 扩展新数据集

### 1. 注册新数据集
```python
from utils.data_utils import DatasetRegistry

# 定义新数据集配置
new_dataset_config = {
    'name': 'New_Dataset',
    'description': '新数据集描述',
    'source': 'Amazon Reviews 2023',
    'text_features': ['title', 'description'],
    'expected_stats': {
        'users': 10000,
        'items': 15000,
        'interactions': 100000
    }
}

# 注册数据集
DatasetRegistry.register_dataset('New_Dataset', new_dataset_config)
```

### 2. 添加配置文件支持
```yaml
# config/collaborative_config.yaml
data:
  dataset_configs:
    New_Dataset:
      expected_users: 10000
      expected_items: 15000
      expected_interactions: 100000
      text_features: ["title", "description"]
      min_interactions: 5
```

### 3. 准备数据文件
```
data/New_Dataset/
├── New_Dataset_train.txt
├── New_Dataset_valid.txt
├── New_Dataset_test.txt
└── text_name_dict.json.gz
```

## 📈 性能优化

### 1. 数据加载优化
- 支持多进程数据加载
- 内存映射大文件
- 批量预处理和缓存

### 2. 配置缓存
- 配置文件解析缓存
- 数据集元信息缓存
- 统计信息缓存

### 3. 懒加载机制
- 按需加载数据集
- 延迟初始化大对象
- 内存使用优化

## 🔍 故障排除

### 常见问题

1. **数据集未找到**
   ```
   FileNotFoundError: Required data files not found for dataset 'XXX'
   ```
   解决方案：确保数据集文件存在且命名正确

2. **配置文件错误**
   ```
   Configuration validation failed
   ```
   解决方案：检查配置文件格式和必需字段

3. **内存不足**
   ```
   CUDA out of memory
   ```
   解决方案：减少batch_size或使用CPU模式

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 验证数据集支持
from utils.data_utils import DatasetRegistry
print(DatasetRegistry.is_supported('Your_Dataset'))

# 检查配置
from utils.config_manager import ConfigManager
config_manager = ConfigManager()
print(config_manager.validate_config())
```

## 🎉 总结

重新设计的多数据集架构为Novel项目提供了：

✅ **统一接口**: 所有数据集使用相同的API  
✅ **配置驱动**: 通过配置轻松切换数据集  
✅ **LLM-SRec兼容**: 保持完全的数据格式兼容性  
✅ **易于扩展**: 支持新数据集的快速添加  
✅ **模块化设计**: 清晰的职责分离和代码组织  

这个架构使Novel项目能够像LLM-SRec一样支持多数据集的便捷切换和复现，为端云协同推荐系统的研究和开发提供了坚实的基础。

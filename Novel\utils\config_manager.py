"""
Novel项目配置管理器

提供统一的配置管理功能，支持多数据集的参数化配置，
确保与LLM-SRec项目的配置方式保持一致。

主要功能：
1. 配置文件加载和验证
2. 数据集特定配置管理
3. 命令行参数解析
4. 配置合并和覆盖
5. 配置验证和默认值设置
"""

import os
import yaml
import json
import argparse
from typing import Dict, List, Tuple, Optional, Any, Union
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

# 导入数据集注册表
try:
    from .data_utils import DatasetRegistry
except ImportError:
    import sys
    sys.path.append(str(Path(__file__).parent))
    from data_utils import DatasetRegistry


class ConfigManager:
    """
    配置管理器
    
    提供统一的配置管理接口，支持：
    - YAML配置文件加载
    - 命令行参数解析
    - 数据集特定配置
    - 配置验证和默认值
    """
    
    def __init__(self, config_file: str = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径（可选）
        """
        self.config_file = config_file
        self.config = {}
        self.default_config = self._get_default_config()
        
        if config_file and os.path.exists(config_file):
            self.load_config(config_file)
        else:
            self.config = self.default_config.copy()
            logger.info("Using default configuration")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置
        
        Returns:
            默认配置字典
        """
        return {
            'experiment_name': 'novel_experiment',
            'seed': 42,
            'device': 'cuda:0',
            'log_level': 'INFO',
            
            'data': {
                'dataset': 'Movies_and_TV',
                'data_dir': './data',
                'raw_data_dir': './data/raw',
                'processed_data_dir': './data/processed',
                'min_interactions': 5,
                'max_sequence_length': 128,
                'train_ratio': 0.8,
                'val_ratio': 0.1,
                'test_ratio': 0.1,
                'data_augmentation': False,
                'augmentation_ratio': 0.1
            },
            
            'small_model': {
                'model_type': 'cf_srec',
                'hidden_units': 64,
                'num_blocks': 2,
                'num_heads': 1,
                'dropout_rate': 0.2,
                'max_sequence_length': 128
            },
            
            'large_model': {
                'model_type': 'llm_recommender',
                'llm_model': 'llama-3b',
                'load_in_8bit': True,
                'max_length': 512,
                'temperature': 1.0,
                'use_lora': True,
                'lora_r': 16,
                'lora_alpha': 32,
                'lora_dropout': 0.1
            },
            
            'training': {
                'batch_size': 32,
                'learning_rate': 1e-4,
                'num_epochs': 50,
                'early_stopping_patience': 10,
                'save_best_model': True,
                'eval_steps': 1000
            },
            
            'evaluation': {
                'k_values': [5, 10, 20],
                'metrics': ['ndcg', 'recall', 'precision', 'hit_rate', 'mrr'],
                'batch_size': 64
            }
        }
    
    def load_config(self, config_file: str):
        """
        加载配置文件
        
        Args:
            config_file: 配置文件路径
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                if config_file.endswith('.yaml') or config_file.endswith('.yml'):
                    loaded_config = yaml.safe_load(f)
                elif config_file.endswith('.json'):
                    loaded_config = json.load(f)
                else:
                    raise ValueError(f"Unsupported config file format: {config_file}")
            
            # 合并配置
            self.config = self._merge_configs(self.default_config, loaded_config)
            self.config_file = config_file
            
            logger.info(f"Configuration loaded from: {config_file}")
            
        except Exception as e:
            logger.error(f"Failed to load config file {config_file}: {e}")
            logger.info("Using default configuration")
            self.config = self.default_config.copy()
    
    def _merge_configs(self, default: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """
        递归合并配置字典
        
        Args:
            default: 默认配置
            loaded: 加载的配置
            
        Returns:
            合并后的配置
        """
        merged = default.copy()
        
        for key, value in loaded.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                merged[key] = self._merge_configs(merged[key], value)
            else:
                merged[key] = value
        
        return merged
    
    def update_config(self, updates: Dict[str, Any]):
        """
        更新配置
        
        Args:
            updates: 配置更新字典
        """
        self.config = self._merge_configs(self.config, updates)
        logger.info("Configuration updated")
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取完整配置
        
        Returns:
            配置字典
        """
        return self.config.copy()
    
    def get_data_config(self) -> Dict[str, Any]:
        """
        获取数据配置
        
        Returns:
            数据配置字典
        """
        return self.config.get('data', {}).copy()
    
    def get_model_config(self, model_type: str = 'small_model') -> Dict[str, Any]:
        """
        获取模型配置
        
        Args:
            model_type: 模型类型 ('small_model' 或 'large_model')
            
        Returns:
            模型配置字典
        """
        return self.config.get(model_type, {}).copy()
    
    def get_training_config(self) -> Dict[str, Any]:
        """
        获取训练配置
        
        Returns:
            训练配置字典
        """
        return self.config.get('training', {}).copy()
    
    def set_dataset(self, dataset_name: str):
        """
        设置数据集并应用数据集特定配置
        
        Args:
            dataset_name: 数据集名称
        """
        if not DatasetRegistry.is_supported(dataset_name):
            logger.warning(f"Dataset {dataset_name} not in registry")
        else:
            dataset_config = DatasetRegistry.get_dataset_config(dataset_name)
            logger.info(f"Applying configuration for dataset: {dataset_name}")
            logger.info(f"Dataset description: {dataset_config.get('description', 'N/A')}")
        
        # 更新数据集名称
        self.config['data']['dataset'] = dataset_name
        
        # 应用数据集特定配置（如果有）
        dataset_configs = self.config.get('data', {}).get('dataset_configs', {})
        if dataset_name in dataset_configs:
            dataset_specific = dataset_configs[dataset_name]
            logger.info(f"Applying dataset-specific configuration: {dataset_specific}")
            
            # 更新数据配置
            for key, value in dataset_specific.items():
                if key not in ['expected_users', 'expected_items', 'expected_interactions']:
                    self.config['data'][key] = value
    
    def validate_config(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            配置是否有效
        """
        try:
            # 验证必需的配置项
            required_keys = ['data', 'small_model', 'large_model', 'training']
            for key in required_keys:
                if key not in self.config:
                    logger.error(f"Missing required configuration section: {key}")
                    return False
            
            # 验证数据集
            dataset_name = self.config['data'].get('dataset')
            if not dataset_name:
                logger.error("Dataset name not specified")
                return False
            
            # 验证数据目录
            data_dir = self.config['data'].get('data_dir')
            if not data_dir or not os.path.exists(data_dir):
                logger.error(f"Data directory not found: {data_dir}")
                return False
            
            logger.info("Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
    
    def save_config(self, output_file: str):
        """
        保存配置到文件
        
        Args:
            output_file: 输出文件路径
        """
        try:
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                if output_file.endswith('.yaml') or output_file.endswith('.yml'):
                    yaml.dump(self.config, f, default_flow_style=False, indent=2)
                elif output_file.endswith('.json'):
                    json.dump(self.config, f, indent=2)
                else:
                    raise ValueError(f"Unsupported output format: {output_file}")
            
            logger.info(f"Configuration saved to: {output_file}")
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")


def create_argument_parser() -> argparse.ArgumentParser:
    """
    创建命令行参数解析器（LLM-SRec风格）
    
    Returns:
        配置好的参数解析器
    """
    parser = argparse.ArgumentParser(description='Novel Edge-Cloud Collaborative Recommendation System')
    
    # 基本参数
    parser.add_argument('--config', type=str, help='Configuration file path')
    parser.add_argument('--dataset', type=str, default='Movies_and_TV', 
                       help='Dataset name (Movies_and_TV, Industrial_and_Scientific, etc.)')
    parser.add_argument('--device', type=str, default='0', help='Device ID or "cpu"')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    
    # 数据参数
    parser.add_argument('--data_dir', type=str, default='./data', help='Data directory')
    parser.add_argument('--max_sequence_length', type=int, default=128, help='Maximum sequence length')
    parser.add_argument('--batch_size', type=int, default=32, help='Batch size')
    
    # 训练参数
    parser.add_argument('--learning_rate', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--num_epochs', type=int, default=50, help='Number of epochs')
    parser.add_argument('--train', action='store_true', help='Enable training mode')
    parser.add_argument('--eval', action='store_true', help='Enable evaluation mode')
    
    # 模型参数
    parser.add_argument('--llm_model', type=str, default='llama-3b', help='LLM model name')
    parser.add_argument('--hidden_units', type=int, default=64, help='Hidden units for small model')
    
    return parser


def parse_args_and_config(args: argparse.Namespace = None) -> ConfigManager:
    """
    解析命令行参数并创建配置管理器
    
    Args:
        args: 解析的命令行参数（可选）
        
    Returns:
        配置管理器实例
    """
    if args is None:
        parser = create_argument_parser()
        args = parser.parse_args()
    
    # 创建配置管理器
    config_manager = ConfigManager(args.config if hasattr(args, 'config') else None)
    
    # 应用命令行参数覆盖
    overrides = {}
    
    if hasattr(args, 'dataset') and args.dataset:
        config_manager.set_dataset(args.dataset)
    
    if hasattr(args, 'device') and args.device:
        overrides['device'] = f'cuda:{args.device}' if args.device.isdigit() else args.device
    
    if hasattr(args, 'seed') and args.seed:
        overrides['seed'] = args.seed
    
    # 数据参数覆盖
    data_overrides = {}
    if hasattr(args, 'data_dir') and args.data_dir:
        data_overrides['data_dir'] = args.data_dir
    if hasattr(args, 'max_sequence_length') and args.max_sequence_length:
        data_overrides['max_sequence_length'] = args.max_sequence_length
    if hasattr(args, 'batch_size') and args.batch_size:
        data_overrides['batch_size'] = args.batch_size
    
    if data_overrides:
        overrides['data'] = data_overrides
    
    # 训练参数覆盖
    training_overrides = {}
    if hasattr(args, 'learning_rate') and args.learning_rate:
        training_overrides['learning_rate'] = args.learning_rate
    if hasattr(args, 'num_epochs') and args.num_epochs:
        training_overrides['num_epochs'] = args.num_epochs
    if hasattr(args, 'batch_size') and args.batch_size:
        training_overrides['batch_size'] = args.batch_size
    
    if training_overrides:
        overrides['training'] = training_overrides
    
    # 应用覆盖
    if overrides:
        config_manager.update_config(overrides)
    
    return config_manager

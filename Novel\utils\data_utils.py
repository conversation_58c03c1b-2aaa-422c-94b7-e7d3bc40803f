"""
Novel项目通用数据处理工具模块

支持多个Amazon数据集的加载、预处理和格式转换，
采用工厂模式和注册表机制，确保与LLM-SRec项目的数据处理流程保持一致。

支持的数据集：
1. Movies_and_TV - 电影和电视节目推荐
2. Industrial_and_Scientific - 工业和科学用品推荐
3. Sports_and_Outdoors - 体育和户外用品推荐
4. 其他Amazon 2023数据集（可扩展）

主要功能：
1. 通用数据集加载和预处理
2. 数据格式转换和标准化
3. 训练/验证/测试数据分割
4. 与LLM-SRec完全兼容的数据格式输出
"""

import os
import pickle
import json
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Tuple, Optional, Any, Union
from collections import defaultdict
import logging
from tqdm import tqdm
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


# ==================== 数据集注册表 ====================

class DatasetRegistry:
    """
    数据集注册表

    管理所有支持的数据集配置和元信息，
    提供统一的数据集发现和配置机制。
    """

    _datasets = {
        'Movies_and_TV': {
            'name': 'Movies_and_TV',
            'description': '电影和电视节目推荐数据集',
            'source': 'Amazon Reviews 2023',
            'text_features': ['title', 'description'],
            'expected_stats': {
                'users': 11947,
                'items': 17490,
                'interactions': 144071
            }
        },

        'Industrial_and_Scientific': {
            'name': 'Industrial_and_Scientific',
            'description': '工业和科学用品推荐数据集',
            'source': 'Amazon Reviews 2023',
            'text_features': ['title', 'description', 'brand'],
            'expected_stats': {
                'users': 15000,  # 预估值
                'items': 20000,
                'interactions': 180000
            }
        },

        'Sports_and_Outdoors': {
            'name': 'Sports_and_Outdoors',
            'description': '体育和户外用品推荐数据集',
            'source': 'Amazon Reviews 2023',
            'text_features': ['title', 'description', 'brand', 'category'],
            'expected_stats': {
                'users': 25000,  # 预估值
                'items': 30000,
                'interactions': 300000
            }
        }
    }

    @classmethod
    def get_dataset_config(cls, dataset_name: str) -> Dict[str, Any]:
        """获取数据集配置"""
        if dataset_name not in cls._datasets:
            raise ValueError(f"Unsupported dataset: {dataset_name}. "
                           f"Supported datasets: {list(cls._datasets.keys())}")
        return cls._datasets[dataset_name].copy()

    @classmethod
    def list_datasets(cls) -> List[str]:
        """列出所有支持的数据集"""
        return list(cls._datasets.keys())

    @classmethod
    def register_dataset(cls, name: str, config: Dict[str, Any]):
        """注册新数据集"""
        cls._datasets[name] = config
        logger.info(f"Registered new dataset: {name}")

    @classmethod
    def is_supported(cls, dataset_name: str) -> bool:
        """检查数据集是否支持"""
        return dataset_name in cls._datasets


# ==================== 通用数据集基类 ====================

class BaseRecommendationDataset(Dataset, ABC):
    """
    推荐系统数据集基类

    定义了所有推荐数据集的通用接口和行为，
    确保与LLM-SRec项目的数据格式完全兼容。
    """

    def __init__(self, data_file: str, text_dict_file: str = None,
                 max_seq_len: int = 128, dataset_name: str = None):
        """
        初始化数据集基类

        Args:
            data_file: 数据文件路径 (如 Movies_and_TV_train.txt)
            text_dict_file: 物品文本字典文件路径 (text_name_dict.json.gz)
            max_seq_len: 最大序列长度
            dataset_name: 数据集名称
        """
        self.data_file = data_file
        self.text_dict_file = text_dict_file
        self.max_seq_len = max_seq_len
        self.dataset_name = dataset_name or "Unknown"

        # 获取数据集配置
        if DatasetRegistry.is_supported(self.dataset_name):
            self.dataset_config = DatasetRegistry.get_dataset_config(self.dataset_name)
        else:
            self.dataset_config = {}

        # 加载数据
        self.user_sequences = self._load_sequences()
        self.text_dict = self._load_text_dict() if text_dict_file else None

        logger.info(f"Loaded {len(self.user_sequences)} user sequences from {data_file}")

    def _load_sequences(self) -> Dict[int, List[int]]:
        """
        加载用户序列数据（标准LLM-SRec格式）

        Returns:
            Dict[user_id, List[item_id]]: 用户序列字典
        """
        user_sequences = defaultdict(list)

        with open(self.data_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    try:
                        user_id, item_id = map(int, line.split())
                        user_sequences[user_id].append(item_id)
                    except ValueError:
                        logger.warning(f"Invalid line format: {line}")
                        continue

        return dict(user_sequences)

    def _load_text_dict(self) -> Optional[Dict]:
        """
        加载物品文本字典（LLM-SRec标准格式）

        Returns:
            物品文本字典，包含title、description等信息
        """
        if not self.text_dict_file or not os.path.exists(self.text_dict_file):
            logger.warning(f"Text dictionary file not found: {self.text_dict_file}")
            return None

        try:
            with open(self.text_dict_file, 'rb') as f:
                text_dict = pickle.load(f)

            # 验证文本字典格式
            expected_features = self.dataset_config.get('text_features', ['title', 'description'])
            available_features = list(text_dict.keys())

            logger.info(f"Loaded text dictionary with features: {available_features}")
            logger.info(f"Expected features: {expected_features}")

            if 'title' in text_dict:
                logger.info(f"Text dictionary contains {len(text_dict['title'])} items")

            return text_dict
        except Exception as e:
            logger.error(f"Failed to load text dictionary: {e}")
            return None

    def __len__(self) -> int:
        return len(self.user_sequences)

    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        获取单个样本（标准格式）

        Returns:
            Dict包含：
            - user_id: 用户ID
            - sequence: 物品序列
            - sequence_length: 序列长度
            - dataset_name: 数据集名称
            - text_dict: 文本字典（可选）
        """
        user_ids = list(self.user_sequences.keys())
        user_id = user_ids[idx]
        sequence = self.user_sequences[user_id]

        # 截断或填充序列
        if len(sequence) > self.max_seq_len:
            sequence = sequence[-self.max_seq_len:]

        return {
            'user_id': user_id,
            'sequence': sequence,
            'sequence_length': len(sequence),
            'dataset_name': self.dataset_name,
            'text_dict': self.text_dict
        }

    @abstractmethod
    def get_dataset_stats(self) -> Dict[str, Any]:
        """获取数据集统计信息（子类实现）"""
        pass


class AmazonRecommendationDataset(BaseRecommendationDataset):
    """
    Amazon推荐数据集的具体实现

    支持所有Amazon 2023数据集，包括：
    - Movies_and_TV
    - Industrial_and_Scientific
    - Sports_and_Outdoors
    - 其他Amazon类别数据集
    """

    def __init__(self, data_file: str, text_dict_file: str = None,
                 max_seq_len: int = 128, dataset_name: str = None):
        """
        初始化Amazon推荐数据集

        Args:
            data_file: 数据文件路径
            text_dict_file: 文本字典文件路径
            max_seq_len: 最大序列长度
            dataset_name: 数据集名称（如 'Movies_and_TV'）
        """
        super().__init__(data_file, text_dict_file, max_seq_len, dataset_name)

    def get_dataset_stats(self) -> Dict[str, Any]:
        """
        获取数据集统计信息

        Returns:
            数据集统计信息字典
        """
        # 计算实际统计信息
        all_items = set()
        total_interactions = 0
        sequence_lengths = []

        for user_id, sequence in self.user_sequences.items():
            all_items.update(sequence)
            total_interactions += len(sequence)
            sequence_lengths.append(len(sequence))

        stats = {
            'dataset_name': self.dataset_name,
            'num_users': len(self.user_sequences),
            'num_items': len(all_items),
            'num_interactions': total_interactions,
            'avg_sequence_length': np.mean(sequence_lengths) if sequence_lengths else 0,
            'min_sequence_length': min(sequence_lengths) if sequence_lengths else 0,
            'max_sequence_length': max(sequence_lengths) if sequence_lengths else 0,
            'max_item_id': max(all_items) if all_items else 0,
            'min_item_id': min(all_items) if all_items else 0,
            'has_text_dict': self.text_dict is not None,
            'text_features': list(self.text_dict.keys()) if self.text_dict else []
        }

        # 添加预期统计信息对比
        if self.dataset_config:
            expected_stats = self.dataset_config.get('expected_stats', {})
            stats['expected_users'] = expected_stats.get('users', 'Unknown')
            stats['expected_items'] = expected_stats.get('items', 'Unknown')
            stats['expected_interactions'] = expected_stats.get('interactions', 'Unknown')

        return stats


# ==================== 通用数据加载器 ====================

class UniversalRecommendationDataLoader:
    """
    通用推荐系统数据加载器

    支持多个Amazon数据集的加载和预处理，
    采用工厂模式自动适配不同数据集格式，
    确保与LLM-SRec项目完全兼容。
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化通用数据加载器

        Args:
            config: 数据配置字典，包含以下关键参数：
                - dataset: 数据集名称 (如 'Movies_and_TV')
                - data_dir: 数据根目录
                - max_sequence_length: 最大序列长度
                - 其他预处理参数
        """
        self.config = config
        self.dataset_name = config.get('dataset', 'Movies_and_TV')
        self.data_dir = config.get('data_dir', './data')
        self.max_seq_len = config.get('max_sequence_length', 128)

        # 验证数据集支持
        if not DatasetRegistry.is_supported(self.dataset_name):
            raise ValueError(f"Unsupported dataset: {self.dataset_name}. "
                           f"Supported datasets: {DatasetRegistry.list_datasets()}")

        # 获取数据集配置
        self.dataset_config = DatasetRegistry.get_dataset_config(self.dataset_name)

        # 构建数据文件路径（LLM-SRec标准格式）
        self.data_path = os.path.join(self.data_dir, self.dataset_name)
        self.train_file = os.path.join(self.data_path, f'{self.dataset_name}_train.txt')
        self.valid_file = os.path.join(self.data_path, f'{self.dataset_name}_valid.txt')
        self.test_file = os.path.join(self.data_path, f'{self.dataset_name}_test.txt')
        self.text_dict_file = os.path.join(self.data_path, 'text_name_dict.json.gz')

        logger.info(f"Initializing data loader for dataset: {self.dataset_name}")
        logger.info(f"Dataset description: {self.dataset_config.get('description', 'N/A')}")

        # 验证文件存在性
        self._validate_files()

        # 加载数据集
        self.train_dataset = None
        self.valid_dataset = None
        self.test_dataset = None
        self._load_datasets()
    
    def _validate_files(self):
        """验证数据文件是否存在"""
        required_files = [self.train_file, self.valid_file, self.test_file]
        missing_files = []

        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)

        if missing_files:
            error_msg = f"Required data files not found for dataset '{self.dataset_name}':\n"
            for file_path in missing_files:
                error_msg += f"  - {file_path}\n"
            error_msg += f"\nPlease ensure the {self.dataset_name} dataset is properly downloaded and preprocessed."
            raise FileNotFoundError(error_msg)

        # 检查文本字典文件（可选）
        if not os.path.exists(self.text_dict_file):
            logger.warning(f"Text dictionary file not found: {self.text_dict_file}")
            logger.warning("Text features will not be available for this dataset")

        logger.info(f"All required data files found for dataset: {self.dataset_name}")

    def _load_datasets(self):
        """加载训练、验证、测试数据集"""
        logger.info(f"Loading {self.dataset_name} datasets...")

        # 使用通用Amazon数据集类
        self.train_dataset = AmazonRecommendationDataset(
            self.train_file,
            self.text_dict_file,
            self.max_seq_len,
            self.dataset_name
        )

        self.valid_dataset = AmazonRecommendationDataset(
            self.valid_file,
            self.text_dict_file,
            self.max_seq_len,
            self.dataset_name
        )

        self.test_dataset = AmazonRecommendationDataset(
            self.test_file,
            self.text_dict_file,
            self.max_seq_len,
            self.dataset_name
        )

        logger.info(f"All {self.dataset_name} datasets loaded successfully")
    
    def get_train_loader(self, batch_size: int = 32, shuffle: bool = True) -> DataLoader:
        """获取训练数据加载器"""
        return DataLoader(
            self.train_dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            collate_fn=self._collate_fn
        )
    
    def get_val_loader(self, batch_size: int = 32, shuffle: bool = False) -> DataLoader:
        """获取验证数据加载器"""
        return DataLoader(
            self.valid_dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            collate_fn=self._collate_fn
        )
    
    def get_test_loader(self, batch_size: int = 32, shuffle: bool = False) -> DataLoader:
        """获取测试数据加载器"""
        return DataLoader(
            self.test_dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            collate_fn=self._collate_fn
        )
    
    def _collate_fn(self, batch: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批处理整理函数
        
        Args:
            batch: 批次数据列表
            
        Returns:
            整理后的批次数据
        """
        user_ids = [item['user_id'] for item in batch]
        sequences = [item['sequence'] for item in batch]
        sequence_lengths = [item['sequence_length'] for item in batch]
        
        # 填充序列到相同长度
        max_len = max(sequence_lengths)
        padded_sequences = []
        
        for seq in sequences:
            if len(seq) < max_len:
                # 使用0填充
                padded_seq = seq + [0] * (max_len - len(seq))
            else:
                padded_seq = seq
            padded_sequences.append(padded_seq)
        
        return {
            'user_ids': torch.tensor(user_ids, dtype=torch.long),
            'sequences': torch.tensor(padded_sequences, dtype=torch.long),
            'sequence_lengths': torch.tensor(sequence_lengths, dtype=torch.long),
            'text_dict': batch[0]['text_dict'] if batch else None
        }
    
    def get_dataset_stats(self) -> Dict[str, Any]:
        """
        获取数据集统计信息

        Returns:
            数据集统计信息字典
        """
        # 获取训练集的详细统计信息
        train_stats = self.train_dataset.get_dataset_stats()

        # 合并所有数据集的统计信息
        stats = {
            'dataset_name': self.dataset_name,
            'dataset_description': self.dataset_config.get('description', 'N/A'),
            'train_users': len(self.train_dataset),
            'valid_users': len(self.valid_dataset),
            'test_users': len(self.test_dataset),
            'max_sequence_length': self.max_seq_len,
            'text_features': self.dataset_config.get('text_features', [])
        }

        # 添加训练集的详细统计
        stats.update({
            'total_items': train_stats['num_items'],
            'total_interactions': train_stats['num_interactions'],
            'avg_sequence_length': train_stats['avg_sequence_length'],
            'min_sequence_length': train_stats['min_sequence_length'],
            'max_item_id': train_stats['max_item_id'],
            'has_text_dict': train_stats['has_text_dict'],
            'available_text_features': train_stats['text_features']
        })

        # 添加预期统计信息对比
        expected_stats = self.dataset_config.get('expected_stats', {})
        if expected_stats:
            stats['expected_users'] = expected_stats.get('users', 'Unknown')
            stats['expected_items'] = expected_stats.get('items', 'Unknown')
            stats['expected_interactions'] = expected_stats.get('interactions', 'Unknown')

        return stats


# ==================== 工厂方法和便捷函数 ====================

def create_data_loader(dataset_name: str, config: Dict[str, Any] = None) -> UniversalRecommendationDataLoader:
    """
    数据加载器工厂方法

    Args:
        dataset_name: 数据集名称
        config: 数据配置字典（可选）

    Returns:
        配置好的数据加载器实例
    """
    if config is None:
        config = {}

    # 设置数据集名称
    config['dataset'] = dataset_name

    # 设置默认配置
    default_config = {
        'data_dir': './data',
        'max_sequence_length': 128,
        'batch_size': 32
    }

    # 合并配置
    for key, value in default_config.items():
        if key not in config:
            config[key] = value

    return UniversalRecommendationDataLoader(config)


def list_available_datasets() -> List[str]:
    """
    列出所有可用的数据集

    Returns:
        数据集名称列表
    """
    return DatasetRegistry.list_datasets()


def get_dataset_info(dataset_name: str) -> Dict[str, Any]:
    """
    获取数据集信息

    Args:
        dataset_name: 数据集名称

    Returns:
        数据集配置信息
    """
    return DatasetRegistry.get_dataset_config(dataset_name)


def convert_to_llm_srec_format(data_loader: UniversalRecommendationDataLoader,
                              output_dir: str) -> None:
    """
    将数据转换为LLM-SRec兼容格式

    Args:
        data_loader: Novel通用数据加载器
        output_dir: 输出目录
    """
    os.makedirs(output_dir, exist_ok=True)

    # 获取数据集统计信息
    stats = data_loader.get_dataset_stats()

    # 保存统计信息
    stats_file = os.path.join(output_dir, f'{data_loader.dataset_name}_stats.json')
    with open(stats_file, 'w') as f:
        json.dump(stats, f, indent=2)

    logger.info(f"Dataset conversion completed for {data_loader.dataset_name}")
    logger.info(f"Stats saved to {stats_file}")
    logger.info(f"Dataset statistics: {stats}")


# ==================== 向后兼容性别名 ====================

# 为了保持向后兼容性，提供旧类名的别名
RecommendationDataLoader = UniversalRecommendationDataLoader
MoviesAndTVDataset = AmazonRecommendationDataset

"""
Novel项目数据处理工具模块

支持Movies_and_TV数据集的加载、预处理和格式转换，
确保与LLM-SRec项目的数据处理流程保持一致，便于对比实验。

主要功能：
1. Movies_and_TV数据集加载
2. 数据格式转换和预处理
3. 训练/验证/测试数据分割
4. 与LLM-SRec兼容的数据格式输出
"""

import os
import pickle
import json
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict
import logging
from tqdm import tqdm

logger = logging.getLogger(__name__)


class MoviesAndTVDataset(Dataset):
    """
    Movies_and_TV数据集类
    
    与LLM-SRec项目保持一致的数据格式：
    - 用户ID和物品ID都是整数
    - 序列数据格式：user_id item_id
    - 支持训练/验证/测试三种模式
    """
    
    def __init__(self, data_file: str, text_dict_file: str = None, max_seq_len: int = 128):
        """
        初始化数据集
        
        Args:
            data_file: 数据文件路径 (Movies_and_TV_train.txt等)
            text_dict_file: 物品文本字典文件路径 (text_name_dict.json.gz)
            max_seq_len: 最大序列长度
        """
        self.data_file = data_file
        self.text_dict_file = text_dict_file
        self.max_seq_len = max_seq_len
        
        # 加载数据
        self.user_sequences = self._load_sequences()
        self.text_dict = self._load_text_dict() if text_dict_file else None
        
        logger.info(f"Loaded {len(self.user_sequences)} user sequences from {data_file}")
    
    def _load_sequences(self) -> Dict[int, List[int]]:
        """
        加载用户序列数据
        
        Returns:
            Dict[user_id, List[item_id]]: 用户序列字典
        """
        user_sequences = defaultdict(list)
        
        with open(self.data_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    user_id, item_id = map(int, line.split())
                    user_sequences[user_id].append(item_id)
        
        return dict(user_sequences)
    
    def _load_text_dict(self) -> Optional[Dict]:
        """
        加载物品文本字典
        
        Returns:
            物品文本字典，包含title、description等信息
        """
        if not self.text_dict_file or not os.path.exists(self.text_dict_file):
            return None
            
        try:
            with open(self.text_dict_file, 'rb') as f:
                text_dict = pickle.load(f)
            logger.info(f"Loaded text dictionary with {len(text_dict.get('title', {}))} items")
            return text_dict
        except Exception as e:
            logger.warning(f"Failed to load text dictionary: {e}")
            return None
    
    def __len__(self) -> int:
        return len(self.user_sequences)
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        获取单个样本
        
        Returns:
            Dict包含：
            - user_id: 用户ID
            - sequence: 物品序列
            - target: 目标物品（用于训练）
            - sequence_length: 序列长度
        """
        user_ids = list(self.user_sequences.keys())
        user_id = user_ids[idx]
        sequence = self.user_sequences[user_id]
        
        # 截断或填充序列
        if len(sequence) > self.max_seq_len:
            sequence = sequence[-self.max_seq_len:]
        
        return {
            'user_id': user_id,
            'sequence': sequence,
            'sequence_length': len(sequence),
            'text_dict': self.text_dict
        }


class RecommendationDataLoader:
    """
    推荐系统数据加载器
    
    负责加载和预处理Movies_and_TV数据集，
    提供与Novel项目协同训练兼容的数据格式。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据加载器
        
        Args:
            config: 数据配置字典，包含数据路径、预处理参数等
        """
        self.config = config
        self.dataset_name = config.get('dataset', 'Movies_and_TV')
        self.data_dir = config.get('data_dir', './data')
        self.max_seq_len = config.get('max_sequence_length', 128)
        
        # 数据文件路径
        self.data_path = os.path.join(self.data_dir, self.dataset_name)
        self.train_file = os.path.join(self.data_path, f'{self.dataset_name}_train.txt')
        self.valid_file = os.path.join(self.data_path, f'{self.dataset_name}_valid.txt')
        self.test_file = os.path.join(self.data_path, f'{self.dataset_name}_test.txt')
        self.text_dict_file = os.path.join(self.data_path, 'text_name_dict.json.gz')
        
        # 验证文件存在性
        self._validate_files()
        
        # 加载数据集
        self.train_dataset = None
        self.valid_dataset = None
        self.test_dataset = None
        self._load_datasets()
    
    def _validate_files(self):
        """验证数据文件是否存在"""
        required_files = [self.train_file, self.valid_file, self.test_file]
        for file_path in required_files:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Required data file not found: {file_path}")
        
        logger.info("All required data files found")
    
    def _load_datasets(self):
        """加载训练、验证、测试数据集"""
        logger.info("Loading datasets...")
        
        self.train_dataset = MoviesAndTVDataset(
            self.train_file, 
            self.text_dict_file, 
            self.max_seq_len
        )
        
        self.valid_dataset = MoviesAndTVDataset(
            self.valid_file, 
            self.text_dict_file, 
            self.max_seq_len
        )
        
        self.test_dataset = MoviesAndTVDataset(
            self.test_file, 
            self.text_dict_file, 
            self.max_seq_len
        )
        
        logger.info("All datasets loaded successfully")
    
    def get_train_loader(self, batch_size: int = 32, shuffle: bool = True) -> DataLoader:
        """获取训练数据加载器"""
        return DataLoader(
            self.train_dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            collate_fn=self._collate_fn
        )
    
    def get_val_loader(self, batch_size: int = 32, shuffle: bool = False) -> DataLoader:
        """获取验证数据加载器"""
        return DataLoader(
            self.valid_dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            collate_fn=self._collate_fn
        )
    
    def get_test_loader(self, batch_size: int = 32, shuffle: bool = False) -> DataLoader:
        """获取测试数据加载器"""
        return DataLoader(
            self.test_dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            collate_fn=self._collate_fn
        )
    
    def _collate_fn(self, batch: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批处理整理函数
        
        Args:
            batch: 批次数据列表
            
        Returns:
            整理后的批次数据
        """
        user_ids = [item['user_id'] for item in batch]
        sequences = [item['sequence'] for item in batch]
        sequence_lengths = [item['sequence_length'] for item in batch]
        
        # 填充序列到相同长度
        max_len = max(sequence_lengths)
        padded_sequences = []
        
        for seq in sequences:
            if len(seq) < max_len:
                # 使用0填充
                padded_seq = seq + [0] * (max_len - len(seq))
            else:
                padded_seq = seq
            padded_sequences.append(padded_seq)
        
        return {
            'user_ids': torch.tensor(user_ids, dtype=torch.long),
            'sequences': torch.tensor(padded_sequences, dtype=torch.long),
            'sequence_lengths': torch.tensor(sequence_lengths, dtype=torch.long),
            'text_dict': batch[0]['text_dict'] if batch else None
        }
    
    def get_dataset_stats(self) -> Dict[str, Any]:
        """
        获取数据集统计信息
        
        Returns:
            数据集统计信息字典
        """
        stats = {
            'train_users': len(self.train_dataset),
            'valid_users': len(self.valid_dataset),
            'test_users': len(self.test_dataset),
            'max_sequence_length': self.max_seq_len
        }
        
        # 计算物品数量
        all_items = set()
        for dataset in [self.train_dataset, self.valid_dataset, self.test_dataset]:
            for user_seq in dataset.user_sequences.values():
                all_items.update(user_seq)
        
        stats['total_items'] = len(all_items)
        stats['max_item_id'] = max(all_items) if all_items else 0
        
        return stats


def convert_to_llm_srec_format(data_loader: RecommendationDataLoader, 
                              output_dir: str) -> None:
    """
    将数据转换为LLM-SRec兼容格式
    
    Args:
        data_loader: Novel数据加载器
        output_dir: 输出目录
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取数据集统计信息
    stats = data_loader.get_dataset_stats()
    
    # 保存统计信息
    with open(os.path.join(output_dir, 'dataset_stats.json'), 'w') as f:
        json.dump(stats, f, indent=2)
    
    logger.info(f"Dataset conversion completed. Stats saved to {output_dir}")
    logger.info(f"Dataset statistics: {stats}")

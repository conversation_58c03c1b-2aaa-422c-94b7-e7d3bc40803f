# FELLRec架构图代码映射说明

## 📊 架构图解析

基于提供的联邦学习架构图，本文档详细说明FELLRec项目中各个架构组件与代码文件的对应关系。

### 🏗️ 架构组件概览

```
用户设备 ←→ 客户端 ←→ 客户端模型 ←→ 服务器 ←→ 全局模型
   ↑                        ↓
   └────── 推荐结果 ←────────┘
```

## 📋 架构组件与代码映射表

| 架构组件 | 主要文件 | 关键函数/类 | 代码行数 | 核心功能 |
|---------|---------|------------|---------|---------|
| **用户设备** | `data/games/*.json` | 数据文件 | - | 存储用户历史行为和元数据 |
| **客户端** | `finetune.py` | `train()` 主函数 | 187-1052 | 客户端数据管理和训练协调 |
| **客户端模型** | `finetune.py` + `utils.py` | LoRA配置 + 模型分割 | 450-521, utils.py | LoRA微调和客户端-服务器分割 |
| **服务器** | `utils.py` | `aggregate()` | utils.py | 模型聚合和相似度计算 |
| **全局模型** | `utils.py` | `get_aggregate_lora_weight()` | utils.py | 权重聚合和参数更新 |
| **推荐结果** | `inference.py` | `main()` + `evaluate()` | 70-421 | 模型推理和结果生成 |

## 🔄 数据流向代码实现

### 1. 数据流：用户设备 → 客户端

**架构说明**：用户数据从设备传输到客户端进行本地处理

**代码实现**：
```python
# 文件：finetune.py (第591-617行)
def load_and_split_data():
    # 检查是否已存在分割好的客户端数据
    if not os.path.exists('./data/train_client_data.pkl'):
        # 基于用户嵌入进行智能数据分割
        client_data, val_data, test_data = split_dataset(
            train_data, client_num, val_data, test_data, pretrain_emb_path
        )
        # 保存分割结果
        with open('./data/train_client_data.pkl', 'wb') as file:
            pickle.dump(client_data, file)
    else:
        # 加载已分割的数据
        with open('./data/train_client_data.pkl', 'rb') as file:
            client_data = pickle.load(file)
```

**关键特点**：
- 使用K-means聚类基于用户嵌入进行智能分割
- 确保相似用户分配到同一客户端
- 数据以pickle格式本地缓存

### 2. 本地训练：客户端 → 客户端模型

**架构说明**：客户端使用本地数据训练个性化模型

**代码实现**：
```python
# 文件：finetune.py (第710-845行)
def client_local_training():
    for i in range(client_num):
        # 初始化客户端模型
        if epoch == 0 and i != 0:
            client[i] = LlamaForCausalLM.from_pretrained(base_model, load_in_8bit=True)
            client[i] = prepare_model_for_int8_training(client[i])
            client[i] = get_peft_model(client[i], config)
        
        # 配置训练器
        client_trainer[i] = Trainer(
            model=client[i],
            train_dataset=client_data[i],  # 客户端本地数据
            args=TrainingArguments(...)
        )
        
        # 执行本地训练
        client_trainer[i].train()
        
        # 保存训练结果
        client[i].save_pretrained(f'{output_dir}/client{i}_{save_name}')
```

**关键特点**：
- 每个客户端独立训练LoRA权重
- 使用8位量化和半精度优化内存
- 训练完成后保存客户端特定的模型权重

### 3. 上传参数：客户端模型 → 服务器

**架构说明**：客户端将训练好的模型参数上传到服务器

**代码实现**：
```python
# 文件：utils.py
def aggregate(output_dir, device_map, client_num, save_name, base_model):
    """
    服务器端聚合函数：收集所有客户端的模型参数
    """
    # 加载所有客户端的LoRA权重
    client_models = []
    for i in range(client_num):
        model = LlamaForCausalLM.from_pretrained(base_model)
        model = PeftModel.from_pretrained(
            model, f'{output_dir}/client{i}_{save_name}'
        )
        client_models.append(model)
    
    # 计算客户端间相似度矩阵
    similarity_matrix = compute_similarity_matrix(client_models)
    
    return similarity_matrix, accumulated_params
```

**关键特点**：
- 服务器收集所有客户端的LoRA权重
- 计算客户端模型间的余弦相似度
- 准备聚合所需的参数

### 4. 参数聚合：服务器 → 全局模型

**架构说明**：服务器聚合多个客户端的参数形成全局模型

**代码实现**：
```python
# 文件：finetune.py (第847-905行) + utils.py
def federated_aggregation():
    # 计算相似度矩阵和聚合参数
    sim_matrix, accumulated_params = aggregate(
        output_dir, device_map, client_num, save_name, base_model
    )
    
    # 将训练损失转换为权重分布
    train_loss = softmax_with_temperature(train_loss)
    
    for i in range(client_num):
        # 计算动态聚合权重
        warm_weight[i] = math.tanh(alpha/(train_loss[i]**(epoch+1/beta)))
        
        # 获取聚合后的LoRA权重
        lora_weight = get_aggregate_lora_weight(
            i, sim_matrix, accumulated_params, warm_weight[i], beta
        )
        
        # 更新客户端模型
        client[i].load_state_dict(lora_weight, strict=False)
        client[i].save_pretrained(f'{output_dir}/client{i}_{update_name}')
```

**关键特点**：
- 基于相似度矩阵进行智能聚合
- 使用动态权重调整聚合强度
- 生成更新后的全局模型参数

### 5. 下载参数：全局模型 → 客户端模型

**架构说明**：客户端下载聚合后的全局模型参数

**代码实现**：
```python
# 文件：finetune.py (第738行 + 868行)
def download_global_parameters():
    if epoch != 0:
        # 重新加载基础模型
        client[i] = LlamaForCausalLM.from_pretrained(base_model)
        client[i] = prepare_model_for_int8_training(client[i])
        client[i] = get_peft_model(client[i], config)
        
        # 加载聚合后的LoRA权重
        state_dict = torch.load(f'{output_dir}/client{i}_{save_name}/adapter_model.bin')
        client[i] = set_peft_model_state_dict(client[i], state_dict)
```

**关键特点**：
- 客户端重新初始化模型结构
- 加载服务器聚合后的权重
- 为下一轮训练做准备

### 6. 推理：客户端模型 → 推荐结果

**架构说明**：使用训练好的模型进行推荐推理

**代码实现**：
```python
# 文件：inference.py (第215-248行)
def model_inference():
    def evaluate(instructions, inputs, **kwargs):
        # 构建输入提示
        prompt = [generate_prompt(instruction, input) 
                 for instruction, input in zip(instructions, inputs)]
        
        # 分词处理
        inputs = tokenizer(prompt, return_tensors="pt", padding=True)
        
        # 模型生成
        with torch.no_grad():
            generation_output = model.generate(
                **inputs,
                generation_config=generation_config,
                max_new_tokens=max_new_tokens,
            )
        
        # 解码结果
        output = tokenizer.batch_decode(generation_output.sequences)
        return [_.split('Response:\n')[-1] for _ in output]
```

**关键特点**：
- 将推荐任务转换为文本生成任务
- 使用批量推理提高效率
- 生成自然语言形式的推荐结果

### 7. 返回：推荐结果 → 用户设备

**架构说明**：将推荐结果返回给用户设备

**代码实现**：
```python
# 文件：inference.py (第297-329行)
def return_recommendations():
    # 为每个测试样本添加预测结果
    for i, test in enumerate(test_data):
        test_data[i]['predict'] = outputs[i]
    
    # 保存推理结果
    result_json_data = f'games_client{client_idx}.json'
    if client_idx == 0:
        result = test_data
    else:
        result.extend(test_data)
    
    # 写入JSON文件
    with open(result_json_data, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=4, ensure_ascii=False)
```

**关键特点**：
- 结果以JSON格式保存
- 包含原始输入和模型预测
- 支持多客户端结果合并

## 🔗 文件间调用关系

### 主要执行流程

```
1. train.sh → finetune.py (启动训练)
2. finetune.py → utils.py (数据分割、模型聚合)
3. finetune.py → inference.py (推理生成)
4. inference.py → data/games/evaluate.py (性能评估)
```

### 关键函数调用链

```python
# 训练阶段
finetune.py:train()
├── utils.py:split_dataset()           # 数据分割
├── finetune.py:client_training_loop() # 客户端训练
├── utils.py:aggregate()               # 参数聚合
└── utils.py:get_aggregate_lora_weight() # 权重更新

# 推理阶段
inference.py:main()
├── inference.py:evaluate()            # 批量推理
└── data/games/evaluate.py             # 性能评估
```

## 📊 核心技术实现

### LoRA微调实现
```python
# 文件：finetune.py (第466-475行)
config = LoraConfig(
    r=lora_r,                    # LoRA秩
    lora_alpha=lora_alpha,       # 缩放参数
    target_modules=lora_target_modules,  # 目标模块
    lora_dropout=lora_dropout,   # Dropout率
    bias="none",
    task_type="CAUSAL_LM",
)
client[0] = get_peft_model(client[0], config)
```

### 智能聚合算法
```python
# 文件：utils.py
def get_aggregate_lora_weight(client_index, sim_matrix, accumulated_params, weight, beta):
    """基于相似度的LoRA权重聚合"""
    # 根据相似度矩阵计算聚合权重
    aggregated_weight = weighted_average(
        client_weights, similarity_scores, dynamic_weight
    )
    return aggregated_weight
```

### 客户端-服务器分割
```python
# 文件：utils.py
def split_client_server(model, k):
    """将模型分为客户端和服务器部分"""
    # 前k层 + 输出层 → 客户端
    # 中间层 → 服务器
    return model_server, model_client
```

## 🔍 详细代码示例

### 用户设备数据格式
```json
// 文件：data/games/train_1024_user.json
{
    "instruction": "根据用户的游戏历史，推荐下一个可能喜欢的游戏",
    "input": "用户玩过：《塞尔达传说：旷野之息》、《超级马里奥：奥德赛》",
    "output": "推荐：《马里奥卡丁车8豪华版》",
    "user": 12345
}
```

### 客户端数据分割算法
```python
# 文件：utils.py - split_dataset()函数
def split_dataset(train_data, n, val_data, test_data, pretrain_emb_path):
    """
    基于用户嵌入的智能数据分割

    Args:
        train_data: 训练数据
        n: 客户端数量
        pretrain_emb_path: 预训练用户嵌入路径

    Returns:
        client_data: 分割后的客户端数据列表
    """
    # 1. 加载预训练用户嵌入
    checkpoint = torch.load(pretrain_emb_path, map_location='cpu')
    user_embeddings = checkpoint['state_dict']['user_embedding.weight']

    # 2. 使用K-means聚类分组用户
    from sklearn.cluster import KMeans
    kmeans = KMeans(n_clusters=n, random_state=42)
    user_clusters = kmeans.fit_predict(user_embeddings.numpy())

    # 3. 根据聚类结果分配数据到客户端
    client_data = [[] for _ in range(n)]
    for item in train_data:
        user_id = item['user']
        cluster_id = user_clusters[user_id]
        client_data[cluster_id].append(item)

    return client_data, val_data_split, test_data_split
```

### 联邦聚合核心算法
```python
# 文件：utils.py - aggregate()函数
def aggregate(output_dir, device_map, client_num, save_name, base_model):
    """
    计算客户端相似度矩阵并准备聚合参数
    """
    # 1. 加载所有客户端模型
    client_models = []
    for i in range(client_num):
        model_path = f'{output_dir}/client{i}_{save_name}'
        model = LlamaForCausalLM.from_pretrained(base_model, device_map=device_map)
        model = PeftModel.from_pretrained(model, model_path, device_map=device_map)
        client_models.append(model)

    # 2. 提取LoRA参数
    client_params = []
    for model in client_models:
        lora_params = get_peft_model_state_dict(model)
        # 展平参数向量
        flattened = torch.cat([p.flatten() for p in lora_params.values()])
        client_params.append(flattened)

    # 3. 计算余弦相似度矩阵
    similarity_matrix = torch.zeros(client_num, client_num)
    for i in range(client_num):
        for j in range(client_num):
            similarity = torch.cosine_similarity(
                client_params[i].unsqueeze(0),
                client_params[j].unsqueeze(0)
            )
            similarity_matrix[i][j] = similarity.item()

    return similarity_matrix, client_params
```

### 动态权重聚合策略
```python
# 文件：utils.py - get_aggregate_lora_weight()函数
def get_aggregate_lora_weight(client_index, sim_matrix, accumulated_params, weight, beta):
    """
    基于相似度的LoRA权重聚合

    Args:
        client_index: 当前客户端索引
        sim_matrix: 客户端相似度矩阵
        accumulated_params: 累积的客户端参数
        weight: 动态权重
        beta: 权重调节参数
    """
    # 1. 获取当前客户端与其他客户端的相似度
    similarities = sim_matrix[client_index]

    # 2. 计算聚合权重
    aggregation_weights = torch.softmax(similarities * beta, dim=0)

    # 3. 加权聚合LoRA参数
    aggregated_params = {}
    for key in accumulated_params[0].keys():
        weighted_sum = torch.zeros_like(accumulated_params[0][key])
        for i, params in enumerate(accumulated_params):
            weighted_sum += aggregation_weights[i] * weight * params[key]
        aggregated_params[key] = weighted_sum

    return aggregated_params
```

### 推理生成流程
```python
# 文件：inference.py - evaluate()函数
def evaluate(instructions, inputs, temperature=0, top_p=0.9, num_beams=4, max_new_tokens=128):
    """
    批量推理函数
    """
    # 1. 构建输入提示
    prompts = []
    for instruction, input_text in zip(instructions, inputs):
        prompt = f"""Below is an instruction that describes a task, paired with an input.

### Instruction:
{instruction}

### Input:
{input_text}

### Response:
"""
        prompts.append(prompt)

    # 2. 分词处理
    inputs = tokenizer(
        prompts,
        return_tensors="pt",
        padding=True,
        truncation=True,
        max_length=512
    ).to(device)

    # 3. 配置生成参数
    generation_config = GenerationConfig(
        temperature=temperature,      # 控制随机性
        top_p=top_p,                 # 核采样
        num_beams=num_beams,         # 束搜索
        num_return_sequences=1,      # 返回序列数
        do_sample=True if temperature > 0 else False,
        pad_token_id=tokenizer.pad_token_id,
        eos_token_id=tokenizer.eos_token_id,
    )

    # 4. 模型生成
    with torch.no_grad():
        generation_output = model.generate(
            **inputs,
            generation_config=generation_config,
            max_new_tokens=max_new_tokens,
            return_dict_in_generate=True,
            output_scores=True,
        )

    # 5. 解码和后处理
    sequences = generation_output.sequences
    decoded_outputs = tokenizer.batch_decode(sequences, skip_special_tokens=True)

    # 提取响应部分
    recommendations = []
    for output in decoded_outputs:
        if 'Response:' in output:
            recommendation = output.split('Response:')[-1].strip()
        else:
            recommendation = output.strip()
        recommendations.append(recommendation)

    return recommendations
```

## 📈 性能监控和评估

### 训练过程监控
```python
# 文件：finetune.py - 训练循环中的监控代码
def monitor_training_progress():
    # 记录每个客户端的训练损失
    train_losses = []
    for i in range(client_num):
        final_loss = client_trainer[i].state.log_history[-1]['train_loss']
        train_losses.append(final_loss)
        logging.info(f'客户端 {i} 训练损失: {final_loss:.4f}')

    # 计算全局评估损失
    eval_results = []
    for i in range(client_num):
        eval_loss = eval_trainer[i].evaluate()["eval_loss"]
        eval_results.append(eval_loss)

    # 加权平均计算全局性能
    global_eval_loss = sum(eval_results) / len(eval_results)
    logging.info(f'全局评估损失: {global_eval_loss:.4f}')

    return global_eval_loss
```

### 推荐质量评估
```python
# 文件：data/games/evaluate.py
def evaluate_recommendation_quality(predictions_file):
    """
    评估推荐质量
    """
    with open(predictions_file, 'r') as f:
        results = json.load(f)

    # 计算Top-K准确率
    top1_correct = 0
    top5_correct = 0
    top10_correct = 0

    for item in results:
        true_recommendation = item['output']
        predicted_recommendation = item['predict']

        # 提取推荐的游戏列表
        true_games = extract_games(true_recommendation)
        pred_games = extract_games(predicted_recommendation)

        # 计算命中率
        if len(pred_games) > 0 and pred_games[0] in true_games:
            top1_correct += 1
        if len(set(pred_games[:5]) & set(true_games)) > 0:
            top5_correct += 1
        if len(set(pred_games[:10]) & set(true_games)) > 0:
            top10_correct += 1

    total_samples = len(results)
    metrics = {
        'top1_accuracy': top1_correct / total_samples,
        'top5_accuracy': top5_correct / total_samples,
        'top10_accuracy': top10_correct / total_samples,
    }

    return metrics
```

## 🔧 关键技术特性

### 1. 隐私保护机制
- **数据本地化**：用户数据不离开客户端设备
- **参数聚合**：只共享模型参数，不共享原始数据
- **差分隐私**：可选添加噪声保护（代码中可扩展）

### 2. 计算效率优化
- **LoRA微调**：只训练1-2%的参数，大幅降低计算成本
- **8位量化**：减少50%的内存使用
- **梯度累积**：支持大批次训练而不增加内存

### 3. 通信效率优化
- **稀疏更新**：只传输LoRA权重而非完整模型
- **智能聚合**：基于相似度减少无效聚合
- **异步训练**：支持客户端异步参与训练

## 📊 实验配置建议

### 基础配置
```bash
# 小规模测试
python finetune.py \
    --client_num 3 \
    --num_epochs 5 \
    --batch_size 32 \
    --sample 100

# 完整实验
python finetune.py \
    --client_num 5 \
    --num_epochs 75 \
    --batch_size 64 \
    --learning_rate 1e-4 \
    --lora_r 8 \
    --alpha 0.7
```

### 超参数调优
- **客户端数量**：3-10个，平衡隐私和性能
- **LoRA秩**：4-32，控制模型容量
- **聚合参数α**：0.5-0.9，调节聚合强度
- **学习率**：1e-5到1e-3，根据数据规模调整

这个架构图完美地展示了FELLRec项目中联邦学习的完整实现，每个组件都在代码中有明确的对应关系和实现逻辑。通过这个映射说明，您可以清楚地理解从理论架构到代码实现的完整转换过程。

"""
SASRec模型实现文件
Self-Attentive Sequential Recommendation (SASRec) 模型
用于序列推荐任务的基于Transformer的深度学习模型

在LLM-SRec项目中，此模型作为CF-SRec预训练模型，
为后续的知识蒸馏提供用户序列表示
"""

import numpy as np
import torch
import torch.nn as nn

class PointWiseFeedForward(torch.nn.Module):
    """
    逐点前馈网络 (Point-wise Feed-Forward Network)

    SASRec模型中Transformer块的前馈网络组件
    使用两个1D卷积层实现逐点的非线性变换

    Args:
        hidden_units (int): 隐藏层维度
        dropout_rate (float): Dropout概率，用于防止过拟合
    """
    def __init__(self, hidden_units, dropout_rate):
        super(PointWiseFeedForward, self).__init__()

        # 第一个1D卷积层：hidden_units -> hidden_units
        self.conv1 = torch.nn.Conv1d(hidden_units, hidden_units, kernel_size=1)
        self.dropout1 = torch.nn.Dropout(p=dropout_rate)
        self.relu = torch.nn.ReLU()  # ReLU激活函数

        # 第二个1D卷积层：hidden_units -> hidden_units
        self.conv2 = torch.nn.Conv1d(hidden_units, hidden_units, kernel_size=1)
        self.dropout2 = torch.nn.Dropout(p=dropout_rate)

    def forward(self, inputs):
        """
        前向传播

        Args:
            inputs (torch.Tensor): 输入张量，形状为 [batch_size, seq_len, hidden_units]

        Returns:
            torch.Tensor: 输出张量，形状与输入相同
        """
        # 转置以适应Conv1d的输入格式 [batch_size, hidden_units, seq_len]
        # 执行：Conv1d -> Dropout -> ReLU -> Conv1d -> Dropout
        outputs = self.dropout2(self.conv2(self.relu(self.dropout1(self.conv1(inputs.transpose(-1, -2))))))

        # 转置回原始格式 [batch_size, seq_len, hidden_units]
        outputs = outputs.transpose(-1, -2)

        # 残差连接：输出 = 变换后的特征 + 原始输入
        outputs += inputs
        return outputs

class SASRec(torch.nn.Module):
    """
    SASRec (Self-Attentive Sequential Recommendation) 模型

    基于Transformer架构的序列推荐模型，使用自注意力机制捕获用户行为序列中的依赖关系

    模型架构：
    1. 物品嵌入层 + 位置嵌入层
    2. 多层Transformer块（自注意力 + 前馈网络）
    3. 最终层归一化

    在LLM-SRec项目中的作用：
    - 作为CF-SRec预训练模型，学习用户序列的隐藏表示
    - 为LLM-SRec模型提供知识蒸馏的源表示

    Args:
        user_num (int): 用户数量
        item_num (int): 物品数量
        args: 包含模型超参数的配置对象
    """
    def __init__(self, user_num, item_num, args):
        super(SASRec, self).__init__()

        # 保存初始化参数，用于模型保存和加载
        self.kwargs = {'user_num': user_num, 'item_num':item_num, 'args':args}

        # 基本参数设置
        self.user_num = user_num          # 用户总数
        self.item_num = item_num          # 物品总数
        self.dev = args.device            # 计算设备 (CPU/GPU/HPU)
        self.embedding_dim = args.hidden_units  # 嵌入维度
        self.nn_parameter = args.nn_parameter   # 是否使用nn.Parameter (用于HPU兼容)

        # 物品嵌入层和位置嵌入层初始化
        # 根据设备类型选择不同的初始化方式
        if self.nn_parameter:
            # HPU设备使用nn.Parameter方式
            # 物品嵌入：将物品ID映射为dense向量，+1是为了padding
            self.item_emb = nn.Parameter(torch.normal(0,1, size = (self.item_num+1, args.hidden_units)))
            # 位置嵌入：为序列中每个位置学习位置表示
            self.pos_emb = nn.Parameter(torch.normal(0,1, size=(args.maxlen, args.hidden_units)))
        else:
            # 标准GPU/CPU设备使用nn.Embedding
            self.item_emb = torch.nn.Embedding(self.item_num+1, args.hidden_units, padding_idx=0)
            self.item_emb.weight.data.normal_(0.0,1)  # 正态分布初始化
            self.pos_emb = torch.nn.Embedding(args.maxlen, args.hidden_units)

        # 嵌入层Dropout，防止过拟合
        self.emb_dropout = torch.nn.Dropout(p=args.dropout_rate)

        # Transformer块的组件列表
        self.attention_layernorms = torch.nn.ModuleList()  # 注意力层的LayerNorm
        self.attention_layers = torch.nn.ModuleList()      # 多头自注意力层
        self.forward_layernorms = torch.nn.ModuleList()    # 前馈网络的LayerNorm
        self.forward_layers = torch.nn.ModuleList()        # 前馈网络层

        # 最终输出的LayerNorm
        self.last_layernorm = torch.nn.LayerNorm(args.hidden_units, eps=1e-8)

        self.args = args

        # 构建多层Transformer块
        for _ in range(args.num_blocks):
            # 注意力层的LayerNorm
            new_attn_layernorm = torch.nn.LayerNorm(args.hidden_units, eps=1e-8)
            self.attention_layernorms.append(new_attn_layernorm)

            # 多头自注意力层
            # 使用因果掩码确保只能关注到当前位置之前的物品
            new_attn_layer = torch.nn.MultiheadAttention(args.hidden_units,
                                                            args.num_heads,
                                                            args.dropout_rate)
            self.attention_layers.append(new_attn_layer)

            # 前馈网络的LayerNorm
            new_fwd_layernorm = torch.nn.LayerNorm(args.hidden_units, eps=1e-8)
            self.forward_layernorms.append(new_fwd_layernorm)

            # 逐点前馈网络
            new_fwd_layer = PointWiseFeedForward(args.hidden_units, args.dropout_rate)
            self.forward_layers.append(new_fwd_layer)

    def log2feats(self, log_seqs):
        """
        将用户行为序列转换为特征表示

        这是SASRec模型的核心函数，实现了完整的序列特征提取流程：
        1. 物品嵌入 + 位置嵌入
        2. 多层Transformer编码
        3. 输出序列的隐藏表示

        Args:
            log_seqs (numpy.ndarray): 用户行为序列，形状为 [batch_size, seq_len]
                                     每个元素是物品ID，0表示padding

        Returns:
            torch.Tensor: 序列特征表示，形状为 [batch_size, seq_len, hidden_units]
        """
        # 步骤1: 物品嵌入
        # 根据设备类型选择不同的嵌入方式
        if self.nn_parameter:
            # HPU设备：直接索引nn.Parameter
            seqs = self.item_emb[torch.LongTensor(log_seqs).to(self.dev)]
            # 缩放嵌入向量，遵循Transformer论文的做法
            seqs *= self.embedding_dim ** 0.5
        else:
            # 标准设备：使用nn.Embedding
            seqs = self.item_emb(torch.LongTensor(log_seqs).to(self.dev))
            seqs *= self.item_emb.embedding_dim ** 0.5

        # 步骤2: 位置嵌入
        # 为序列中每个位置生成位置索引 [0, 1, 2, ..., seq_len-1]
        positions = np.tile(np.array(range(log_seqs.shape[1])), [log_seqs.shape[0], 1])

        # 添加位置嵌入到物品嵌入
        if self.nn_parameter:
            seqs += self.pos_emb[torch.LongTensor(positions).to(self.dev)]
        else:
            seqs += self.pos_emb(torch.LongTensor(positions).to(self.dev))

        # 应用Dropout防止过拟合
        seqs = self.emb_dropout(seqs)

        # 步骤3: 创建掩码
        # timeline_mask: 标记padding位置 (物品ID为0的位置)
        timeline_mask = torch.BoolTensor(log_seqs == 0).to(self.dev)
        # 将padding位置的嵌入置零
        seqs *= ~timeline_mask.unsqueeze(-1)

        # 步骤4: 创建因果注意力掩码
        # 确保每个位置只能关注到之前的位置，防止信息泄露
        tl = seqs.shape[1]  # 序列长度
        # 下三角矩阵的上三角部分为True，表示需要被掩码的位置
        attention_mask = ~torch.tril(torch.ones((tl, tl), dtype=torch.bool, device=self.dev))

        # 步骤5: 多层Transformer编码
        for i in range(len(self.attention_layers)):
            # 转置以适应MultiheadAttention的输入格式 [seq_len, batch_size, hidden_units]
            seqs = torch.transpose(seqs, 0, 1)

            # Pre-LayerNorm: 先归一化再进行注意力计算
            Q = self.attention_layernorms[i](seqs)

            # 多头自注意力计算
            # Q, K, V都使用相同的输入seqs (自注意力)
            # attention_mask确保因果性约束
            mha_outputs, _ = self.attention_layers[i](Q, seqs, seqs,
                                            attn_mask=attention_mask)

            # 残差连接：注意力输出 + 归一化后的输入
            seqs = Q + mha_outputs

            # 转置回原始格式 [batch_size, seq_len, hidden_units]
            seqs = torch.transpose(seqs, 0, 1)

            # 前馈网络部分
            # Pre-LayerNorm + 前馈网络 + 残差连接
            seqs = self.forward_layernorms[i](seqs)
            seqs = self.forward_layers[i](seqs)

            # 再次应用padding掩码，确保padding位置保持为0
            seqs *= ~timeline_mask.unsqueeze(-1)

        # 步骤6: 最终层归一化
        log_feats = self.last_layernorm(seqs)
        return log_feats

    def forward(self, user_ids, log_seqs, pos_seqs, neg_seqs, mode='default'):
        """
        SASRec模型的前向传播

        根据不同的mode执行不同的计算逻辑：
        - 'default': 计算正负样本的logits，用于训练
        - 'log_only': 只返回用户序列表示，用于知识蒸馏
        - 'item': 返回重塑后的特征，用于特定任务

        Args:
            user_ids (numpy.ndarray): 用户ID，形状为 [batch_size]
            log_seqs (numpy.ndarray): 用户行为序列，形状为 [batch_size, seq_len]
            pos_seqs (numpy.ndarray): 正样本序列，形状为 [batch_size, seq_len]
            neg_seqs (numpy.ndarray): 负样本序列，形状为 [batch_size, seq_len]
            mode (str): 运行模式

        Returns:
            根据mode返回不同的结果：
            - 'default': (pos_logits, neg_logits) - 正负样本的预测分数
            - 'log_only': log_feats - 用户序列的最后一个位置的表示
            - 'item': (log_feats, pos_embs, neg_embs) - 重塑后的特征和嵌入
        """
        # 获取用户行为序列的特征表示
        log_feats = self.log2feats(log_seqs)  # [batch_size, seq_len, hidden_units]

        # 模式1: 只返回序列表示 (用于LLM-SRec的知识蒸馏)
        if mode == 'log_only':
            # 取序列最后一个位置的表示作为用户表示
            log_feats = log_feats[:, -1, :]  # [batch_size, hidden_units]
            return log_feats

        # 获取正样本和负样本的物品嵌入
        if self.nn_parameter:
            # HPU设备：直接索引
            pos_embs = self.item_emb[torch.LongTensor(pos_seqs).to(self.dev)]
            neg_embs = self.item_emb[torch.LongTensor(neg_seqs).to(self.dev)]
        else:
            # 标准设备：使用Embedding层
            pos_embs = self.item_emb(torch.LongTensor(pos_seqs).to(self.dev))
            neg_embs = self.item_emb(torch.LongTensor(neg_seqs).to(self.dev))

        # 计算预测分数
        # 使用点积计算序列表示与物品嵌入的相似度
        pos_logits = (log_feats * pos_embs).sum(dim=-1)  # [batch_size, seq_len]
        neg_logits = (log_feats * neg_embs).sum(dim=-1)  # [batch_size, seq_len]

        # 模式2: 返回重塑后的特征 (用于特定的训练任务)
        if mode == 'item':
            # 将特征重塑为二维张量，便于批量处理
            return (log_feats.reshape(-1, log_feats.shape[2]),
                    pos_embs.reshape(-1, log_feats.shape[2]),
                    neg_embs.reshape(-1, log_feats.shape[2]))
        else:
            # 模式3: 默认模式，返回正负样本的logits (用于BPR损失计算)
            return pos_logits, neg_logits

    def predict(self, user_ids, log_seqs, item_indices):
        """
        预测函数：计算用户对候选物品的偏好分数

        用于推理阶段，给定用户的历史行为序列和候选物品，
        计算用户对这些候选物品的偏好分数，用于排序和推荐

        Args:
            user_ids (numpy.ndarray): 用户ID，形状为 [batch_size]
            log_seqs (numpy.ndarray): 用户行为序列，形状为 [batch_size, seq_len]
            item_indices (numpy.ndarray): 候选物品ID，形状为 [batch_size, num_candidates]

        Returns:
            torch.Tensor: 预测分数，形状为 [batch_size, num_candidates]
                         分数越高表示用户越可能喜欢该物品
        """
        # 获取用户行为序列的特征表示
        log_feats = self.log2feats(log_seqs)  # [batch_size, seq_len, hidden_units]

        # 提取序列最后一个位置的特征作为用户的最终表示
        # 这代表了用户当前的兴趣状态
        final_feat = log_feats[:, -1, :]  # [batch_size, hidden_units]

        # 获取候选物品的嵌入表示
        if self.nn_parameter:
            # HPU设备：直接索引
            item_embs = self.item_emb[torch.LongTensor(item_indices).to(self.dev)]
        else:
            # 标准设备：使用Embedding层
            item_embs = self.item_emb(torch.LongTensor(item_indices).to(self.dev))

        # 计算用户表示与候选物品嵌入的相似度分数
        # 使用矩阵乘法计算批量点积: [batch_size, num_candidates, hidden_units] × [batch_size, hidden_units, 1]
        logits = item_embs.matmul(final_feat.unsqueeze(-1)).squeeze(-1)  # [batch_size, num_candidates]

        return logits
# LLM-SRec项目代码架构分析

## 项目概述

LLM-SRec是一个基于大语言模型的序列推荐系统，通过将预训练的协同过滤序列推荐模型(CF-SRec)的用户表示蒸馏到大语言模型中，增强LLM对序列信息的理解能力。

## 架构流程图对应的代码模块分析

### 1. 用户数据 (User Data)
**对应代码文件：**
- `SeqRec/sasrec/data_preprocess.py` - 数据预处理模块
- `SeqRec/sasrec/utils.py` - 数据工具函数
- `SeqRec/data_*` 目录 - 存储处理后的数据集

**主要功能：**
- 从Amazon 2023数据集下载和预处理用户交互数据
- 将原始数据转换为训练/验证/测试集
- 生成用户序列和物品文本描述映射

**代码示例：**
```python
# 文件: SeqRec/sasrec/data_preprocess.py (13-45行)
def preprocess_raw_5core(fname):
    # 加载Amazon数据集
    dataset = load_dataset("McAuley-Lab/Amazon-Reviews-2023", f"5core_last_out_{fname}")
    meta_dataset = load_dataset("McAuley-Lab/Amazon-Reviews-2023", f"raw_meta_{fname}")

    # 构建物品元数据字典
    meta_dict = {}
    for l in tqdm(meta_dataset['full']):
        meta_dict[l['parent_asin']] = [l['title'], l['description']]

    # 用户和物品映射
    usermap = dict()
    itemmap = dict()
    User = defaultdict(list)

    # 处理训练/验证/测试数据
    for t in ['train', 'valid', 'test']:
        for l in tqdm(dataset[t]):
            user_id = l['user_id']
            asin = l['parent_asin']

            # 分配用户ID
            if user_id not in usermap:
                usernum += 1
                usermap[user_id] = usernum

            # 分配物品ID
            if asin not in itemmap:
                itemnum += 1
                itemmap[asin] = itemnum

            User[usermap[user_id]].append(itemmap[asin])
```

**关键函数：**
- `preprocess_raw_5core()` - 处理5-core数据集
- `data_partition()` - 数据分割
- `SeqDataset` 类 - 序列数据集封装

### 2. LLM4Rec模型 (LLM4Rec Model)
**对应代码文件：**
- `models/seqllm4rec.py` - LLM4Rec模型实现

**主要功能：**
- 基于LLaMA-3.2-3B-Instruct的推荐模型
- 处理文本输入和用户历史序列
- 生成用户和物品的表示向量

**代码示例：**
```python
# 文件: models/seqllm4rec.py (22-48行)
class llm4rec(nn.Module):
    def __init__(self, device, llm_model="llama-3b", args=None):
        super().__init__()
        self.device = device

        # 加载预训练LLM模型
        if llm_model == 'llama-3b':
            model_id = "meta-llama/Llama-3.2-3B-Instruct"

        # 8-bit量化加载模型
        self.llm_model = AutoModelForCausalLM.from_pretrained(
            model_id,
            device_map=self.device,
            torch_dtype=torch.float16,
            load_in_8bit=True
        )

        # 加载分词器
        self.llm_tokenizer = AutoTokenizer.from_pretrained(model_id, use_fast=False)

        # 添加特殊token
        self.llm_tokenizer.add_special_tokens({
            'additional_special_tokens': ['[UserRep]', '[HistoryEmb]', '[UserOut]', '[ItemOut]']
        })

        # 调整词汇表大小
        self.llm_model.resize_token_embeddings(len(self.llm_tokenizer))
```

**关键组件：**
- `llm4rec` 类 - 主要模型类
- `train_mode0()` - 训练模式函数
- 特殊token处理：`[UserOut]`, `[HistoryEmb]`, `[ItemOut]`

### 3. 预训练的CF-SRec模型 (Pre-trained CF-SRec Model)
**对应代码文件：**
- `SeqRec/sasrec/model.py` - SASRec模型实现
- `SeqRec/sasrec/main.py` - SASRec训练脚本
- `models/recsys_model.py` - 推荐系统模型封装

**主要功能：**
- 实现SASRec(Self-Attentive Sequential Recommendation)模型
- 基于Transformer的序列建模
- 生成用户序列的隐藏表示

**代码示例：**
```python
# 文件: SeqRec/sasrec/model.py (83-106行)
def log2feats(self, log_seqs):
    # 物品嵌入
    seqs = self.item_emb(torch.LongTensor(log_seqs).to(self.dev))
    seqs *= self.item_emb.embedding_dim ** 0.5

    # 位置编码
    positions = np.tile(np.array(range(log_seqs.shape[1])), [log_seqs.shape[0], 1])
    seqs += self.pos_emb(torch.LongTensor(positions).to(self.dev))
    seqs = self.emb_dropout(seqs)

    # 注意力掩码
    timeline_mask = torch.BoolTensor(log_seqs == 0).to(self.dev)
    seqs *= ~timeline_mask.unsqueeze(-1)

    # 多层自注意力
    for i in range(len(self.attention_layers)):
        seqs = torch.transpose(seqs, 0, 1)
        Q = self.attention_layernorms[i](seqs)
        mha_outputs, _ = self.attention_layers[i](Q, seqs, seqs, attn_mask=attention_mask)
        seqs = Q + mha_outputs
        seqs = torch.transpose(seqs, 0, 1)

        # 前馈网络
        seqs = self.forward_layernorms[i](seqs)
        seqs = self.forward_layers[i](seqs)
        seqs *= ~timeline_mask.unsqueeze(-1)

    return self.last_layernorm(seqs)

# 文件: models/recsys_model.py (27-42行)
class RecSys(nn.Module):
    def __init__(self, recsys_model, pre_trained_data, device):
        super().__init__()
        # 加载预训练模型
        kwargs, checkpoint = load_checkpoint(recsys_model, pre_trained_data)
        model = SASRec(**kwargs)
        model.load_state_dict(checkpoint)

        # 冻结参数
        for p in model.parameters():
            p.requires_grad = False

        self.model = model.to(device)
```

**关键组件：**
- `SASRec` 类 - 核心序列推荐模型
- `log2feats()` - 序列特征提取
- `RecSys` 类 - 预训练模型加载器

### 4. LLM-SRec模型 (LLM-SRec Model)
**对应代码文件：**
- `models/seqllm_model.py` - LLM-SRec主模型
- `train_model.py` - 训练流程控制

**主要功能：**
- 整合LLM4Rec和CF-SRec模型
- 实现知识蒸馏机制
- 序列推荐的端到端训练

**代码示例：**
```python
# 文件: models/seqllm_model.py (22-59行)
class llmrec_model(nn.Module):
    def __init__(self, args):
        super().__init__()
        self.args = args
        self.device = args.device

        # 加载物品文本描述
        with open(f'./SeqRec/data_{args.rec_pre_trained_data}/text_name_dict.json.gz','rb') as ft:
            self.text_name_dict = pickle.load(ft)

        # 初始化CF-SRec模型
        self.recsys = RecSys(args.recsys, args.rec_pre_trained_data, self.device)

        # 初始化LLM4Rec模型
        self.llm = llm4rec(device=self.device, llm_model=args.llm, args=self.args)

        # 损失函数
        self.mse = nn.MSELoss()
        self.l1 = nn.L1Loss()

# 文件: models/seqllm_model.py (179-189行)
def forward(self, data, optimizer=None, batch_iter=None, mode='phase1'):
    if mode == 'phase2':
        self.pre_train_phase2(data, optimizer, batch_iter)
    elif mode == 'generate_batch':
        self.generate_batch(data)
        print('test (NDCG@10: %.4f, HR@10: %.4f), Num User: %.4f'
              % (self.NDCG/self.users, self.HT/self.users, self.users))
        print('test (NDCG@20: %.4f, HR@20: %.4f), Num User: %.4f'
              % (self.NDCG_20/self.users, self.HIT_20/self.users, self.users))
```

**关键组件：**
- `llmrec_model` 类 - 主要模型类
- `pre_train_phase2()` - 第二阶段训练
- `generate_batch()` - 推理生成

### 5. 推荐结果 (Recommendation Results)
**对应代码文件：**
- `train_model.py` - 评估和结果输出
- 结果保存在 `models/{save_dir}/` 目录

**主要功能：**
- 使用训练好的模型生成个性化推荐
- 计算推荐质量评估指标
- 保存最佳模型和结果

**代码示例：**
```python
# 文件: train_model.py (154-181行)
# 模型评估和结果保存
if perform >= best_perform:
    best_perform = perform
    # 保存最佳模型
    if rank == 0:
        if args.multi_gpu:
            model.module.save_model(args, epoch2=epoch, best=True)
        else:
            model.save_model(args, epoch2=epoch, best=True)

    # 在测试集上评估
    model.users = 0.0
    model.NDCG = 0.0
    model.HT = 0.0
    model.NDCG_20 = 0.0
    model.HIT_20 = 0.0

    with torch.no_grad():
        for _, data in enumerate(inference_data_loader):
            u, seq, pos, neg = data
            u, seq, pos, neg = u.numpy(), seq.numpy(), pos.numpy(), neg.numpy()
            model([u,seq,pos,neg, rank, None, 'original'], mode='generate_batch')

    # 保存结果到文件
    out_dir = f'./models/{args.save_dir}best/{args.rec_pre_trained_data}_{args.llm}_{epoch}_results.txt'
    with open(out_dir, 'a') as f:
        f.write(f'NDCG: {model.NDCG/model.users}, HR: {model.HT/model.users}\n')
        f.write(f'NDCG20: {model.NDCG_20/model.users}, HR20: {model.HIT_20/model.users}\n')
```

**评估指标：**
- NDCG@10, NDCG@20 - 归一化折扣累积增益
- HR@10, HR@20 - 命中率

## 完整执行流程

### 阶段1：CF-SRec模型预训练
```bash
cd SeqRec/sasrec
python main.py --device 0 --dataset Industrial_and_Scientific
```

**执行逻辑：**
1. `SeqRec/sasrec/main.py` 启动
2. 调用 `data_preprocess.py` 下载和预处理数据
3. 使用 `model.py` 中的SASRec模型训练
4. 保存预训练模型到 `SeqRec/sasrec/{dataset}/` 目录

### 阶段2：LLM-SRec模型训练
```bash
python main.py --device 0 --train --rec_pre_trained_data Industrial_and_Scientific
```

**代码示例：**
```python
# 文件: train_model.py (114-121行)
for epoch in tqdm(range(epoch_start_idx, args.num_epochs + 1)):
    model.train()
    for step, data in enumerate(train_data_loader):
        u, seq, pos, neg = data
        u, seq, pos, neg = u.numpy(), seq.numpy(), pos.numpy(), neg.numpy()

        # 调用模型前向传播，mode='phase2'表示第二阶段训练
        model([u,seq,pos,neg],
              optimizer=adam_optimizer,
              batch_iter=[epoch, args.num_epochs + 1, step, num_batch],
              mode='phase2')

# 文件: models/seqllm_model.py (323-340行)
def pre_train_phase2(self, data, optimizer, batch_iter):
    epoch, total_epoch, step, total_step = batch_iter
    u, seq, pos, neg = data

    # 获取CF-SRec模型的用户表示
    log_emb = self.recsys.model(u, seq, pos, neg, mode='log_only')

    # 构造训练样本
    samples = {
        'text_input': text_input,
        'log_emb': log_emb,
        'candidates_pos': candidates_pos,
        'interact': interact_embs,
        'candidate_embs': candidate_embs
    }

    # LLM训练
    loss, rec_loss, match_loss = self.llm(samples, mode=0)

    # 反向传播
    loss.backward()
    optimizer.step()
```

**执行逻辑：**
1. `main.py` 解析参数并调用 `train_model()`
2. `train_model.py` 中的 `train_model_()` 函数执行：
   - 加载预训练的CF-SRec模型
   - 初始化LLM-SRec模型 (`llmrec_model`)
   - 准备训练数据 (`SeqDataset`)
   - 执行训练循环
3. 每个训练步骤：
   - 调用 `model.forward()` 进行前向传播
   - 计算推荐损失和匹配损失
   - 反向传播更新参数
4. 定期验证和测试，保存最佳模型

## 数据流向分析

1. **用户数据输入** → 数据预处理 → 序列化表示
2. **序列数据** → CF-SRec模型 → 用户序列表示
3. **文本数据** → LLM4Rec模型 → 用户/物品文本表示
4. **知识蒸馏** → CF-SRec表示 + LLM表示 → 对齐损失
5. **推理阶段** → LLM-SRec模型 → 推荐结果

## 关键配置参数

- `--llm`: 使用的大语言模型 (默认: llama-3b)
- `--recsys`: 推荐系统模型 (默认: sasrec)
- `--rec_pre_trained_data`: 预训练数据集名称
- `--maxlen`: 最大序列长度 (默认: 128)
- `--batch_size`: 批次大小 (默认: 20)
- `--stage2_lr`: 第二阶段学习率 (默认: 0.0001)

## 模型保存和加载

- 预训练CF-SRec模型保存在: `SeqRec/sasrec/{dataset}/`
- LLM-SRec模型保存在: `models/{save_dir}/`
- 最佳模型保存在: `models/{save_dir}best/`

## 核心技术细节

### 知识蒸馏机制
**实现位置：** `models/seqllm4rec.py` 的 `train_mode0()` 函数

**代码示例：**
```python
# 文件: models/seqllm4rec.py (287-306行)
def train_mode0(self, samples):
    # LLM生成用户表示
    user_outputs = self.pred_user(user_outputs)
    item_outputs = self.pred_item(item_outputs)

    # 推荐损失
    rec_loss = self.rec_loss(user_outputs, item_outputs)

    # CF模型表示预处理
    log_emb = self.pred_user_CF2(samples['log_emb'])

    # 表示归一化
    user_outputs = F.normalize(user_outputs, p=2, dim=1)
    log_emb = F.normalize(log_emb, p=2, dim=1)

    # 知识蒸馏损失：对齐LLM和CF表示
    match_loss = self.mse(user_outputs, log_emb)

    # 添加均匀性正则化防止表示坍塌
    match_loss += (self.uniformity(user_outputs) + self.uniformity(log_emb))

    # 总损失
    loss = rec_loss + match_loss
    return loss, rec_loss.item(), match_loss.item()
```

**蒸馏过程：**
1. CF-SRec模型生成用户序列表示 `log_emb`
2. LLM4Rec模型生成用户表示 `user_outputs`
3. 通过MSE损失对齐两种表示
4. 添加均匀性正则化项防止表示坍塌

### 特殊Token机制
**特殊Token定义：**
- `[UserOut]`: 标记用户表示输出位置
- `[HistoryEmb]`: 标记历史序列嵌入位置
- `[ItemOut]`: 标记物品表示输出位置
- `[UserRep]`: 用户表示标记

**代码示例：**
```python
# 文件: models/seqllm4rec.py (249-284行)
def train_mode0(self, samples):
    # 文本tokenization
    llm_tokens = self.llm_tokenizer(
        samples['text_input'],
        return_tensors="pt",
        padding="longest",
        truncation=True,
        max_length=1024,
    ).to(self.device)

    # 获取输入嵌入
    inputs_embeds = self.llm_model.get_input_embeddings()(llm_tokens['input_ids'])

    # 替换特殊token为对应嵌入
    inputs_embeds = self.replace_out_token_all(
        llm_tokens,
        inputs_embeds,
        token=['[UserOut]', '[HistoryEmb]'],
        embs={'[HistoryEmb]': samples['interact']}
    )

    # LLM前向传播
    outputs = self.llm_model.forward(
        inputs_embeds=inputs_embeds,
        output_hidden_states=True
    )

    # 提取用户表示
    indx = self.get_embeddings(llm_tokens, '[UserOut]')
    user_outputs = torch.cat([
        outputs.hidden_states[-1][i, indx[i]].mean(axis=0).unsqueeze(0)
        for i in range(len(indx))
    ])
```

**处理流程：**
1. 在输入文本中插入特殊token
2. 用对应的嵌入向量替换token位置
3. 从输出隐藏状态中提取对应位置的表示

### 损失函数设计
**总损失组成：**
```python
loss = rec_loss + match_loss
```

**推荐损失 (rec_loss)：**
- 用户-物品交互预测损失
- 基于用户表示和物品表示的相似度

**匹配损失 (match_loss)：**
- CF-SRec和LLM表示的对齐损失
- 包含均匀性正则化项

## 数据处理详细流程

### 1. 原始数据处理
**数据来源：** Amazon Reviews 2023数据集
**处理步骤：**
1. 下载5-core数据集和元数据
2. 构建用户-物品交互序列
3. 生成物品文本描述映射
4. 按时间戳排序用户行为序列

### 2. 序列数据构造
**序列格式：**
- 输入序列：用户历史交互物品ID序列
- 正样本：序列中下一个物品
- 负样本：随机采样的未交互物品

**数据增强：**
- 序列截断和填充到固定长度
- 负采样策略避免已交互物品

### 3. 文本数据构造
**文本模板：**
```
"Based on the user's purchase history: [HistoryEmb], predict the next item the user will purchase. [UserOut]"
```

**物品描述模板：**
```
"Item: {title} Description: {description} [ItemOut]"
```

## 模型架构详细分析

### SASRec模型结构
**核心组件：**
- 物品嵌入层：将物品ID映射为向量
- 位置编码：添加序列位置信息
- 多头自注意力层：捕获序列依赖关系
- 前馈网络：非线性变换
- 层归一化：稳定训练过程

**关键函数：**
- `log2feats()`: 序列到特征的转换
- `forward()`: 前向传播计算损失
- `predict()`: 推理阶段预测

### LLM4Rec模型结构
**基础模型：** LLaMA-3.2-3B-Instruct
**关键修改：**
- 扩展词汇表添加特殊token
- 8-bit量化减少显存占用
- LoRA微调策略

**输入处理：**
1. 文本tokenization
2. 特殊token位置标记
3. 嵌入向量替换
4. 注意力掩码构造

## 训练策略分析

### 两阶段训练
**阶段1：CF-SRec预训练**
- 目标：学习序列模式
- 数据：用户-物品交互序列
- 损失：BPR损失或交叉熵损失

**阶段2：LLM-SRec联合训练**
- 目标：对齐LLM和CF表示
- 数据：文本+序列混合数据
- 损失：推荐损失+蒸馏损失

### 优化策略
**学习率调度：**
- 指数衰减：每轮乘以0.95
- 初始学习率：0.0001

**早停机制：**
- 验证集性能连续5轮不提升则停止
- 保存最佳模型用于测试

**批次处理：**
- 训练批次大小：20
- 推理批次大小：20
- 支持分布式训练

## 评估体系

### 评估指标
**NDCG (Normalized Discounted Cumulative Gain)：**
- 考虑排序位置的推荐质量指标
- 计算@10和@20两个版本

**HR (Hit Rate)：**
- 命中率，衡量推荐准确性
- 计算@10和@20两个版本

### 评估流程
1. 从验证/测试集随机采样用户
2. 对每个用户生成推荐列表
3. 计算各项评估指标
4. 输出平均性能结果

## 部署和使用

### 环境要求
- Python 3.8+
- PyTorch 1.12+
- Transformers 4.20+
- CUDA 11.0+ (GPU训练)

### 快速开始
1. 安装依赖：`pip install -r requirements.txt`
2. 预训练CF-SRec：`cd SeqRec/sasrec && python main.py --dataset Industrial_and_Scientific`
3. 训练LLM-SRec：`python main.py --train --rec_pre_trained_data Industrial_and_Scientific`

## 6. 模型推理：客户端模型 → 推荐结果

**功能：** 使用训练好的模型生成个性化推荐

**代码示例：**
```python
# 文件: models/seqllm_model.py (generate_batch函数)
def generate_batch(self, data):
    u, seq, pos, neg, rank, _, mode = data

    # 获取用户历史序列表示
    log_emb = self.recsys.model(u, seq, pos, neg, mode='log_only')

    # 构造推理输入
    for i in range(len(u)):
        user_id = u[i]
        user_seq = seq[i]

        # 生成文本提示
        prompt = self.generate_prompt(user_seq, user_id)

        # 分词处理
        inputs = self.llm.llm_tokenizer(
            prompt,
            return_tensors="pt",
            max_length=1024,
            truncation=True
        ).to(self.device)

        # 模型生成
        with torch.no_grad():
            generation_output = self.llm.llm_model.generate(
                **inputs,
                generation_config=generation_config,
                max_new_tokens=max_new_tokens,
            )

        # 解码结果
        output = self.llm.llm_tokenizer.batch_decode(generation_output)
        recommendations = [_.split('Response:\n')[-1] for _ in output]

        # 计算评估指标
        self.calculate_metrics(recommendations, pos[i])

def generate_prompt(self, user_seq, user_id):
    # 获取用户历史物品描述
    history_items = []
    for item_id in user_seq:
        if item_id in self.text_name_dict:
            title, desc = self.text_name_dict[item_id]
            history_items.append(f"Item: {title}")

    # 构造提示模板
    prompt = f"""Based on the user's purchase history: {', '.join(history_items)},
    predict the next item the user will purchase. [UserOut]"""

    return prompt
```

**关键点：**
- 将推荐任务转换为文本生成任务
- 使用批量推理提高效率
- 生成自然语言形式的推荐结果

## 总结

LLM-SRec项目通过巧妙的架构设计，将传统协同过滤推荐模型的序列建模能力与大语言模型的文本理解能力相结合，实现了更好的序列推荐效果。整个系统采用两阶段训练策略，先预训练CF-SRec模型获得序列表示，再通过知识蒸馏将这些表示融入到LLM中，形成最终的LLM-SRec模型。

**项目优势：**
- 充分利用预训练CF模型的序列建模能力
- 结合LLM的文本理解和生成能力
- 通过知识蒸馏实现有效的模型融合
- 支持多种数据集和模型配置

**适用场景：**
- 电商推荐系统
- 内容推荐平台
- 序列预测任务
- 多模态推荐场景

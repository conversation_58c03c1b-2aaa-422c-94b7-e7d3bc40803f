#!/usr/bin/env python3
"""
Movies_and_TV数据集预处理脚本

用于验证和预处理Movies_and_TV数据集，确保与LLM-SRec项目的数据格式完全兼容。

使用方法：
    python scripts/preprocess_movies_tv.py --data_dir ./data/Movies_and_TV --output_dir ./data/processed

功能：
1. 验证数据完整性
2. 分析数据集统计信息
3. 生成LLM-SRec兼容格式
4. 创建对比实验配置
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.movies_tv_preprocessor import MoviesAndTVPreprocessor
from utils.data_utils import RecommendationDataLoader, convert_to_llm_srec_format

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('preprocess_movies_tv.log')
    ]
)

logger = logging.getLogger(__name__)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Movies_and_TV数据集预处理')
    
    parser.add_argument(
        '--data_dir', 
        type=str, 
        default='./data/Movies_and_TV',
        help='Movies_and_TV数据集目录路径'
    )
    
    parser.add_argument(
        '--output_dir', 
        type=str, 
        default='./data/processed',
        help='预处理结果输出目录'
    )
    
    parser.add_argument(
        '--validate_only', 
        action='store_true',
        help='仅验证数据完整性，不进行预处理'
    )
    
    parser.add_argument(
        '--generate_stats', 
        action='store_true',
        help='生成详细的数据集统计信息'
    )
    
    parser.add_argument(
        '--llm_srec_format', 
        action='store_true',
        help='生成LLM-SRec兼容格式'
    )
    
    args = parser.parse_args()
    
    # 验证输入路径
    if not os.path.exists(args.data_dir):
        logger.error(f"数据目录不存在: {args.data_dir}")
        return 1
    
    logger.info(f"开始处理Movies_and_TV数据集")
    logger.info(f"数据目录: {args.data_dir}")
    logger.info(f"输出目录: {args.output_dir}")
    
    try:
        # 初始化预处理器
        preprocessor = MoviesAndTVPreprocessor(args.data_dir, args.output_dir)
        
        # 验证数据完整性
        logger.info("=== 步骤1: 验证数据完整性 ===")
        if not preprocessor.validate_data_integrity():
            logger.error("数据完整性验证失败")
            return 1
        
        logger.info("✅ 数据完整性验证通过")
        
        if args.validate_only:
            logger.info("仅验证模式，预处理完成")
            return 0
        
        # 加载和分析数据
        logger.info("=== 步骤2: 加载和分析数据 ===")
        stats = preprocessor.load_and_analyze_data()
        
        # 打印统计信息
        print_dataset_statistics(stats)
        
        if args.generate_stats:
            logger.info("=== 步骤3: 生成详细统计信息 ===")
            generate_detailed_statistics(stats, args.output_dir)
        
        if args.llm_srec_format:
            logger.info("=== 步骤4: 生成LLM-SRec兼容格式 ===")
            preprocessor.generate_llm_srec_compatible_format()
        
        # 测试数据加载器
        logger.info("=== 步骤5: 测试数据加载器 ===")
        test_data_loader(args.data_dir)
        
        logger.info("✅ Movies_and_TV数据集预处理完成")
        return 0
        
    except Exception as e:
        logger.error(f"预处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


def print_dataset_statistics(stats):
    """打印数据集统计信息"""
    print("\n" + "="*50)
    print("Movies_and_TV 数据集统计信息")
    print("="*50)
    
    print(f"📊 基本统计:")
    print(f"  总用户数: {stats['total_users']:,}")
    print(f"  总物品数: {stats['total_items']:,}")
    print(f"  总交互数: {stats['total_interactions']:,}")
    
    print(f"\n📈 数据分割:")
    print(f"  训练交互数: {stats['train_interactions']:,}")
    print(f"  验证交互数: {stats['valid_interactions']:,}")
    print(f"  测试交互数: {stats['test_interactions']:,}")
    
    print(f"\n👥 用户统计:")
    print(f"  用户ID范围: {stats['min_user_id']} - {stats['max_user_id']}")
    print(f"  平均用户交互数: {stats['avg_user_interactions']:.2f}")
    print(f"  用户交互数标准差: {stats['std_user_interactions']:.2f}")
    
    print(f"\n🎬 物品统计:")
    print(f"  物品ID范围: {stats['min_item_id']} - {stats['max_item_id']}")
    print(f"  平均物品交互数: {stats['avg_item_interactions']:.2f}")
    print(f"  物品交互数标准差: {stats['std_item_interactions']:.2f}")
    
    print(f"\n📝 序列统计:")
    print(f"  平均序列长度: {stats['avg_sequence_length']:.2f}")
    print(f"  序列长度标准差: {stats['std_sequence_length']:.2f}")
    print(f"  序列长度范围: {stats['min_sequence_length']} - {stats['max_sequence_length']}")
    
    print(f"\n📚 文本信息:")
    print(f"  包含文本字典: {'是' if stats['has_text_dict'] else '否'}")
    print(f"  文本字典物品数: {stats['text_dict_items']:,}")
    
    print("="*50)


def generate_detailed_statistics(stats, output_dir):
    """生成详细统计信息文件"""
    import json
    import matplotlib.pyplot as plt
    import numpy as np
    
    # 保存详细统计信息
    detailed_stats_file = os.path.join(output_dir, 'detailed_statistics.json')
    
    with open(detailed_stats_file, 'w') as f:
        json.dump(stats, f, indent=2)
    
    logger.info(f"详细统计信息已保存到: {detailed_stats_file}")
    
    # 生成可视化图表（如果matplotlib可用）
    try:
        # 创建图表目录
        charts_dir = os.path.join(output_dir, 'charts')
        os.makedirs(charts_dir, exist_ok=True)
        
        # 数据分布饼图
        plt.figure(figsize=(10, 6))
        
        # 数据分割分布
        plt.subplot(1, 2, 1)
        labels = ['训练', '验证', '测试']
        sizes = [stats['train_interactions'], stats['valid_interactions'], stats['test_interactions']]
        plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
        plt.title('数据分割分布')
        
        # 基本统计柱状图
        plt.subplot(1, 2, 2)
        categories = ['用户数', '物品数', '交互数(千)']
        values = [stats['total_users'], stats['total_items'], stats['total_interactions']/1000]
        plt.bar(categories, values)
        plt.title('数据集基本统计')
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(charts_dir, 'dataset_overview.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"统计图表已保存到: {charts_dir}")
        
    except ImportError:
        logger.warning("matplotlib未安装，跳过图表生成")
    except Exception as e:
        logger.warning(f"图表生成失败: {e}")


def test_data_loader(data_dir):
    """测试数据加载器功能"""
    try:
        # 创建测试配置
        test_config = {
            'dataset': 'Movies_and_TV',
            'data_dir': os.path.dirname(data_dir),
            'max_sequence_length': 128
        }
        
        # 初始化数据加载器
        data_loader = RecommendationDataLoader(test_config)
        
        # 获取数据集统计信息
        stats = data_loader.get_dataset_stats()
        
        # 测试数据加载器
        train_loader = data_loader.get_train_loader(batch_size=4, shuffle=False)
        
        # 获取一个批次进行测试
        for batch in train_loader:
            logger.info(f"测试批次形状:")
            logger.info(f"  用户ID: {batch['user_ids'].shape}")
            logger.info(f"  序列: {batch['sequences'].shape}")
            logger.info(f"  序列长度: {batch['sequence_lengths'].shape}")
            break
        
        logger.info("✅ 数据加载器测试通过")
        
    except Exception as e:
        logger.error(f"数据加载器测试失败: {e}")
        raise


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

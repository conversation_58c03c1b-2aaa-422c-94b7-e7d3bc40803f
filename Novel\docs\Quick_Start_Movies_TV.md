# Movies_and_TV 数据集快速开始指南

本指南帮助您快速开始使用Movies_and_TV数据集进行Novel vs LLM-SRec对比实验。

## 🚀 5分钟快速开始

### 步骤1: 验证数据集 (1分钟)
```bash
# 进入Novel项目目录
cd Novel

# 验证数据完整性
python scripts/preprocess_movies_tv.py --validate_only
```

**预期输出**:
```
✅ 数据完整性验证通过
Movies_and_TV 数据集统计信息
==================================================
📊 基本统计:
  总用户数: 7,468
  总物品数: 16,012
  总交互数: 144,074
```

### 步骤2: 生成实验配置 (1分钟)
```bash
# 创建默认实验配置
python scripts/run_comparison_experiment.py
```

**生成的配置文件**: `experiments/configs/movies_tv_comparison.yaml`

### 步骤3: 运行数据预处理 (2分钟)
```bash
# 生成详细统计和兼容格式
python scripts/preprocess_movies_tv.py \
    --generate_stats \
    --llm_srec_format \
    --output_dir ./data/processed
```

### 步骤4: 测试数据加载 (1分钟)
```python
# 测试脚本 test_data_loading.py
from utils.data_utils import RecommendationDataLoader

config = {
    'dataset': 'Movies_and_TV',
    'data_dir': './data',
    'max_sequence_length': 128
}

data_loader = RecommendationDataLoader(config)
train_loader = data_loader.get_train_loader(batch_size=4)

# 测试一个批次
for batch in train_loader:
    print(f"用户ID形状: {batch['user_ids'].shape}")
    print(f"序列形状: {batch['sequences'].shape}")
    break

print("✅ 数据加载测试成功！")
```

## 🔬 运行对比实验

### 完整实验流程
```bash
# 1. 运行对比实验（需要训练好的Novel模型）
python scripts/run_comparison_experiment.py \
    --config experiments/configs/movies_tv_comparison.yaml \
    --llm_srec_results ./data/Movies_and_TV/Results.txt \
    --model_checkpoint ./models/novel_best.pth

# 2. 查看实验结果
cat experiments/results/movies_tv_comparison/experiment_report.md
```

### 模拟实验（无需训练模型）
```bash
# 使用模拟模型进行测试
python scripts/run_comparison_experiment.py \
    --config experiments/configs/movies_tv_comparison.yaml \
    --llm_srec_results ./data/Movies_and_TV/Results.txt
```

## 📊 预期结果示例

### 数据集统计
```
📊 基本统计:
  总用户数: 7,468
  总物品数: 16,012
  总交互数: 144,074

📈 数据分割:
  训练交互数: 128,695
  验证交互数: 7,911
  测试交互数: 7,468

📝 序列统计:
  平均序列长度: 18.34
  序列长度范围: 1 - 128
```

### 实验报告示例
```markdown
# Novel_vs_LLM-SRec_Movies_and_TV 实验报告

## 实验结果

### Novel模型结果
- ndcg@5: 0.1234
- ndcg@10: 0.1456
- recall@5: 0.0987
- recall@10: 0.1543

### LLM-SRec模型结果
- ndcg@5: 0.1123
- ndcg@10: 0.1334
- recall@5: 0.0876
- recall@10: 0.1432

### 对比结果
#### 改进幅度 (%)
- ndcg@5: 📈 +9.88%
- ndcg@10: 📈 +9.15%
- recall@5: 📈 +12.67%
- recall@10: 📈 +7.75%
```

## 🛠️ 常用命令

### 数据相关
```bash
# 查看数据集信息
python -c "
from utils.data_utils import RecommendationDataLoader
config = {'dataset': 'Movies_and_TV', 'data_dir': './data', 'max_sequence_length': 128}
loader = RecommendationDataLoader(config)
print(loader.get_dataset_stats())
"

# 验证数据格式
head -10 data/Movies_and_TV/Movies_and_TV_train.txt

# 检查文本字典
python -c "
import pickle
with open('data/Movies_and_TV/text_name_dict.json.gz', 'rb') as f:
    data = pickle.load(f)
    print(f'Title items: {len(data.get(\"title\", {}))}')
    print(f'Description items: {len(data.get(\"description\", {}))}')
"
```

### 实验相关
```bash
# 仅验证实验配置
python scripts/run_comparison_experiment.py \
    --config experiments/configs/movies_tv_comparison.yaml \
    --dry_run

# 查看实验结果
ls -la experiments/results/movies_tv_comparison/

# 查看实验日志
tail -f comparison_experiment.log
```

### 评估相关
```bash
# 测试评估器
python -c "
from utils.evaluation import RecommendationEvaluator
import numpy as np

evaluator = RecommendationEvaluator([5, 10, 20])
predictions = np.random.rand(10, 100)  # 10个用户，100个物品
targets = [[1, 5, 10] for _ in range(10)]  # 每个用户的真实物品

metrics = evaluator.evaluate_batch(predictions, targets)
evaluator.print_metrics()
"
```

## 📁 文件结构

完成集成后的文件结构：
```
Novel/
├── data/
│   ├── Movies_and_TV/           # 原始数据集
│   │   ├── Movies_and_TV_train.txt
│   │   ├── Movies_and_TV_valid.txt
│   │   ├── Movies_and_TV_test.txt
│   │   ├── text_name_dict.json.gz
│   │   └── Results.txt
│   └── processed/               # 预处理结果
│       ├── train_sequences.json
│       ├── valid_sequences.json
│       ├── test_sequences.json
│       ├── item_metadata.json
│       └── dataset_statistics.json
├── experiments/
│   ├── configs/
│   │   └── movies_tv_comparison.yaml
│   ├── results/
│   │   └── movies_tv_comparison/
│   └── comparison_framework.py
├── scripts/
│   ├── preprocess_movies_tv.py
│   └── run_comparison_experiment.py
├── utils/
│   ├── data_utils.py
│   ├── movies_tv_preprocessor.py
│   └── evaluation.py
└── docs/
    ├── Movies_and_TV_Integration_Guide.md
    └── Quick_Start_Movies_TV.md
```

## 🔍 故障排除

### 问题1: 数据文件不存在
```bash
# 检查数据文件
ls -la data/Movies_and_TV/
# 如果缺失，重新复制
cp ../LLM-SRec-master/SeqRec/data_Movies_and_TV/* data/Movies_and_TV/
```

### 问题2: Python路径错误
```bash
# 确保在Novel项目根目录
pwd  # 应该显示 .../Novel
export PYTHONPATH=$PWD:$PYTHONPATH
```

### 问题3: 依赖包缺失
```bash
# 安装必要依赖
pip install torch numpy pandas pyyaml tqdm
```

## 📞 获取帮助

如果遇到问题：

1. **查看日志文件**: `preprocess_movies_tv.log`, `comparison_experiment.log`
2. **检查配置文件**: 确保路径和参数正确
3. **运行验证脚本**: 使用 `--validate_only` 和 `--dry_run` 参数
4. **查看详细文档**: [完整集成指南](Movies_and_TV_Integration_Guide.md)

## ✅ 检查清单

完成以下检查确保集成成功：

- [ ] 数据文件存在且格式正确
- [ ] 数据预处理脚本运行成功
- [ ] 数据加载器测试通过
- [ ] 实验配置文件生成
- [ ] 评估器功能正常
- [ ] 对比实验框架可用

完成所有检查后，您就可以开始进行Novel vs LLM-SRec的对比实验了！

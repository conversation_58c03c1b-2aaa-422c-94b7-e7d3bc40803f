#!/usr/bin/env python3
"""
Novel项目多数据集切换演示脚本

展示如何在Novel项目中轻松切换不同的Amazon数据集，
演示重新设计的数据处理架构的便利性。

功能演示：
1. 列出所有支持的数据集
2. 动态切换数据集
3. 加载和分析不同数据集
4. 展示统一的数据处理接口
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.data_utils import (
    create_data_loader, 
    list_available_datasets, 
    get_dataset_info,
    DatasetRegistry
)
from utils.config_manager import ConfigManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def demo_dataset_registry():
    """演示数据集注册表功能"""
    print("\n" + "="*60)
    print("📋 数据集注册表演示")
    print("="*60)
    
    # 列出所有支持的数据集
    datasets = list_available_datasets()
    print(f"支持的数据集数量: {len(datasets)}")
    print("支持的数据集:")
    for i, dataset in enumerate(datasets, 1):
        print(f"  {i}. {dataset}")
    
    # 展示数据集详细信息
    print("\n数据集详细信息:")
    for dataset in datasets:
        info = get_dataset_info(dataset)
        print(f"\n📊 {dataset}:")
        print(f"  描述: {info.get('description', 'N/A')}")
        print(f"  来源: {info.get('source', 'N/A')}")
        print(f"  文本特征: {info.get('text_features', [])}")
        expected = info.get('expected_stats', {})
        print(f"  预期用户数: {expected.get('users', 'N/A'):,}")
        print(f"  预期物品数: {expected.get('items', 'N/A'):,}")
        print(f"  预期交互数: {expected.get('interactions', 'N/A'):,}")


def demo_data_loader_switching():
    """演示数据加载器的数据集切换功能"""
    print("\n" + "="*60)
    print("🔄 数据加载器切换演示")
    print("="*60)
    
    # 基础配置
    base_config = {
        'data_dir': './data',
        'max_sequence_length': 128,
        'batch_size': 32
    }
    
    # 演示切换不同数据集
    datasets_to_demo = ['Movies_and_TV']  # 只演示已有的数据集
    
    for dataset_name in datasets_to_demo:
        print(f"\n🎬 切换到数据集: {dataset_name}")
        print("-" * 40)
        
        try:
            # 创建数据加载器
            data_loader = create_data_loader(dataset_name, base_config)
            
            # 获取数据集统计信息
            stats = data_loader.get_dataset_stats()
            
            print(f"✅ 成功加载 {dataset_name} 数据集")
            print(f"数据集描述: {stats.get('dataset_description', 'N/A')}")
            print(f"训练用户数: {stats.get('train_users', 0):,}")
            print(f"验证用户数: {stats.get('valid_users', 0):,}")
            print(f"测试用户数: {stats.get('test_users', 0):,}")
            print(f"总物品数: {stats.get('total_items', 0):,}")
            print(f"平均序列长度: {stats.get('avg_sequence_length', 0):.2f}")
            print(f"文本特征: {stats.get('available_text_features', [])}")
            
            # 测试数据加载器
            train_loader = data_loader.get_train_loader(batch_size=4, shuffle=False)
            print(f"训练批次数: {len(train_loader)}")
            
            # 获取一个样本批次
            for batch in train_loader:
                print(f"样本批次形状:")
                print(f"  用户ID: {batch['user_ids'].shape}")
                print(f"  序列: {batch['sequences'].shape}")
                print(f"  序列长度: {batch['sequence_lengths'].shape}")
                break
                
        except FileNotFoundError as e:
            print(f"❌ 数据集 {dataset_name} 未找到: {e}")
            print(f"请确保已下载并预处理 {dataset_name} 数据集")
        except Exception as e:
            print(f"❌ 加载 {dataset_name} 时出错: {e}")


def demo_config_management():
    """演示配置管理功能"""
    print("\n" + "="*60)
    print("⚙️ 配置管理演示")
    print("="*60)
    
    # 创建配置管理器
    config_manager = ConfigManager()
    
    print("📋 默认配置:")
    config = config_manager.get_config()
    print(f"默认数据集: {config['data']['dataset']}")
    print(f"默认设备: {config['device']}")
    print(f"默认批次大小: {config['training']['batch_size']}")
    
    # 演示数据集切换
    datasets_to_switch = ['Movies_and_TV', 'Industrial_and_Scientific']
    
    for dataset in datasets_to_switch:
        print(f"\n🔄 切换到数据集: {dataset}")
        
        # 设置数据集
        config_manager.set_dataset(dataset)
        
        # 获取更新后的配置
        updated_config = config_manager.get_data_config()
        print(f"当前数据集: {updated_config['dataset']}")
        
        # 显示数据集特定配置
        if DatasetRegistry.is_supported(dataset):
            dataset_info = get_dataset_info(dataset)
            print(f"数据集描述: {dataset_info.get('description', 'N/A')}")
            print(f"文本特征: {dataset_info.get('text_features', [])}")


def demo_unified_interface():
    """演示统一接口的便利性"""
    print("\n" + "="*60)
    print("🎯 统一接口演示")
    print("="*60)
    
    print("演示如何用相同的代码处理不同数据集:")
    
    def process_dataset(dataset_name: str):
        """处理单个数据集的通用函数"""
        print(f"\n处理数据集: {dataset_name}")
        
        try:
            # 统一的数据加载接口
            config = {'dataset': dataset_name, 'data_dir': './data', 'max_sequence_length': 128}
            data_loader = create_data_loader(dataset_name, config)
            
            # 统一的统计信息获取
            stats = data_loader.get_dataset_stats()
            
            # 统一的数据加载器获取
            train_loader = data_loader.get_train_loader(batch_size=8)
            
            print(f"  ✅ 成功处理 {dataset_name}")
            print(f"  📊 用户数: {stats.get('train_users', 0):,}")
            print(f"  📦 物品数: {stats.get('total_items', 0):,}")
            print(f"  🔢 批次数: {len(train_loader)}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            return False
    
    # 使用相同的函数处理不同数据集
    available_datasets = ['Movies_and_TV']  # 只处理已有的数据集
    
    success_count = 0
    for dataset in available_datasets:
        if process_dataset(dataset):
            success_count += 1
    
    print(f"\n📈 处理结果: {success_count}/{len(available_datasets)} 个数据集成功处理")


def demo_extensibility():
    """演示架构的可扩展性"""
    print("\n" + "="*60)
    print("🔧 可扩展性演示")
    print("="*60)
    
    print("演示如何添加新的数据集支持:")
    
    # 演示注册新数据集
    new_dataset_config = {
        'name': 'Custom_Dataset',
        'description': '自定义演示数据集',
        'source': 'Custom Source',
        'text_features': ['title', 'description', 'custom_field'],
        'expected_stats': {
            'users': 5000,
            'items': 8000,
            'interactions': 50000
        }
    }
    
    print("注册新数据集:")
    print(f"  名称: {new_dataset_config['name']}")
    print(f"  描述: {new_dataset_config['description']}")
    print(f"  文本特征: {new_dataset_config['text_features']}")
    
    # 注册数据集
    DatasetRegistry.register_dataset('Custom_Dataset', new_dataset_config)
    
    # 验证注册
    updated_datasets = list_available_datasets()
    print(f"\n更新后支持的数据集数量: {len(updated_datasets)}")
    
    if 'Custom_Dataset' in updated_datasets:
        print("✅ 新数据集注册成功")
        
        # 获取新数据集信息
        info = get_dataset_info('Custom_Dataset')
        print(f"新数据集信息: {info}")
    else:
        print("❌ 新数据集注册失败")


def main():
    """主演示函数"""
    print("🚀 Novel项目多数据集架构演示")
    print("展示重新设计的数据处理架构如何支持多数据集的便捷切换")
    
    try:
        # 1. 数据集注册表演示
        demo_dataset_registry()
        
        # 2. 数据加载器切换演示
        demo_data_loader_switching()
        
        # 3. 配置管理演示
        demo_config_management()
        
        # 4. 统一接口演示
        demo_unified_interface()
        
        # 5. 可扩展性演示
        demo_extensibility()
        
        print("\n" + "="*60)
        print("🎉 演示完成！")
        print("="*60)
        print("重新设计的架构特点:")
        print("✅ 支持多数据集的统一接口")
        print("✅ 配置驱动的数据集切换")
        print("✅ 与LLM-SRec完全兼容的数据格式")
        print("✅ 易于扩展的注册表机制")
        print("✅ 统一的预处理和加载流程")
        
    except Exception as e:
        logger.error(f"演示过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
